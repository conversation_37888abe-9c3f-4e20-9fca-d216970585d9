IVR MENU RUNNING

   PAMI\Client\Exception\ClientException 

  error: "Connection refused" while create socket "tcp://solutionsv3.tclcontactplus.com:5038"

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:200
    196▕             STREAM_CLIENT_CONNECT,
    197▕             $this->context
    198▕         );
    199▕         if ($this->socket === false) {
  ➜ 200▕             throw new ClientException(sprintf('error: "%s" while create socket "%s"', $errstr, $socketUri));
    201▕         }
    202▕         // set socket in block mode
    203▕         if (!stream_set_blocking($this->socket, true)) {
    204▕             throw new ClientException(sprintf('error set block mode on "%s" socket', $socketUri));

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/IvrMenu.php:68
      PAMI\Client\Impl\ClientImpl::open()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()

IVR MENU RUNNING

   PAMI\Client\Exception\ClientException 

  error: "Connection refused" while create socket "tcp://solutionsv3.tclcontactplus.com:5038"

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:200
    196▕             STREAM_CLIENT_CONNECT,
    197▕             $this->context
    198▕         );
    199▕         if ($this->socket === false) {
  ➜ 200▕             throw new ClientException(sprintf('error: "%s" while create socket "%s"', $errstr, $socketUri));
    201▕         }
    202▕         // set socket in block mode
    203▕         if (!stream_set_blocking($this->socket, true)) {
    204▕             throw new ClientException(sprintf('error set block mode on "%s" socket', $socketUri));

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/IvrMenu.php:68
      PAMI\Client\Impl\ClientImpl::open()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()

IVR MENU RUNNING

   PAMI\Client\Exception\ClientException 

  error: "Connection refused" while create socket "tcp://solutionsv3.tclcontactplus.com:5038"

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:200
    196▕             STREAM_CLIENT_CONNECT,
    197▕             $this->context
    198▕         );
    199▕         if ($this->socket === false) {
  ➜ 200▕             throw new ClientException(sprintf('error: "%s" while create socket "%s"', $errstr, $socketUri));
    201▕         }
    202▕         // set socket in block mode
    203▕         if (!stream_set_blocking($this->socket, true)) {
    204▕             throw new ClientException(sprintf('error set block mode on "%s" socket', $socketUri));

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/IvrMenu.php:68
      PAMI\Client\Impl\ClientImpl::open()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()

IVR MENU RUNNING

   PAMI\Client\Exception\ClientException 

  error: "Connection refused" while create socket "tcp://solutionsv3.tclcontactplus.com:5038"

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:200
    196▕             STREAM_CLIENT_CONNECT,
    197▕             $this->context
    198▕         );
    199▕         if ($this->socket === false) {
  ➜ 200▕             throw new ClientException(sprintf('error: "%s" while create socket "%s"', $errstr, $socketUri));
    201▕         }
    202▕         // set socket in block mode
    203▕         if (!stream_set_blocking($this->socket, true)) {
    204▕             throw new ClientException(sprintf('error set block mode on "%s" socket', $socketUri));

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/IvrMenu.php:68
      PAMI\Client\Impl\ClientImpl::open()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()

IVR MENU RUNNING

   PAMI\Client\Exception\ClientException 

  Error reading

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:292
    288▕         // Read something.
    289▕         //$read = @fread($this->socket, 65535);
    290▕         $read = @fread($this->socket, 8192);
    291▕         if ($read === false || (empty($read) && @feof($this->socket))) {
  ➜ 292▕             throw new ClientException('Error reading');
    293▕         }
    294▕         $this->currentProcessingMessage .= $read;
    295▕         // If we have a complete message, then return it. Save the rest for
    296▕         // later.

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/IvrMenu.php:71
      PAMI\Client\Impl\ClientImpl::process()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()

IVR MENU RUNNING

   PAMI\Client\Exception\ClientException 

  error: "Connection refused" while create socket "tcp://solutionsv3.tclcontactplus.com:5038"

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:200
    196▕             STREAM_CLIENT_CONNECT,
    197▕             $this->context
    198▕         );
    199▕         if ($this->socket === false) {
  ➜ 200▕             throw new ClientException(sprintf('error: "%s" while create socket "%s"', $errstr, $socketUri));
    201▕         }
    202▕         // set socket in block mode
    203▕         if (!stream_set_blocking($this->socket, true)) {
    204▕             throw new ClientException(sprintf('error set block mode on "%s" socket', $socketUri));

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/IvrMenu.php:68
      PAMI\Client\Impl\ClientImpl::open()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()

IVR MENU RUNNING
IVR MENU Processing
Timing Res : 1
Priority menu has no options. Redirecting to 5454 context.
IVR MENU Processing
Timing Res : 1
Priority menu has no options. Redirecting to 5454 context.
IVR MENU Processing
Timing Res : 1
Priority menu has no options. Redirecting to 5454 context.
IVR MENU RUNNING
IVR MENU RUNNING

   PAMI\Client\Exception\ClientException 

  Error reading

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:292
    288▕         // Read something.
    289▕         //$read = @fread($this->socket, 65535);
    290▕         $read = @fread($this->socket, 8192);
    291▕         if ($read === false || (empty($read) && @feof($this->socket))) {
  ➜ 292▕             throw new ClientException('Error reading');
    293▕         }
    294▕         $this->currentProcessingMessage .= $read;
    295▕         // If we have a complete message, then return it. Save the rest for
    296▕         // later.

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/IvrMenu.php:71
      PAMI\Client\Impl\ClientImpl::process()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()

IVR MENU RUNNING

   PAMI\Client\Exception\ClientException 

  error: "Connection refused" while create socket "tcp://solutionsv3.tclcontactplus.com:5038"

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:200
    196▕             STREAM_CLIENT_CONNECT,
    197▕             $this->context
    198▕         );
    199▕         if ($this->socket === false) {
  ➜ 200▕             throw new ClientException(sprintf('error: "%s" while create socket "%s"', $errstr, $socketUri));
    201▕         }
    202▕         // set socket in block mode
    203▕         if (!stream_set_blocking($this->socket, true)) {
    204▕             throw new ClientException(sprintf('error set block mode on "%s" socket', $socketUri));

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/IvrMenu.php:68
      PAMI\Client\Impl\ClientImpl::open()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()

IVR MENU RUNNING
IVR MENU RUNNING
IVR MENU RUNNING
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
Error processing IVR: Error reading
Error processing IVR: Error reading
Error processing IVR: Error reading
Error processing IVR: Error reading
Error processing IVR: Error reading

   PAMI\Client\Exception\ClientException 

  Error reading

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:292
    288▕         // Read something.
    289▕         //$read = @fread($this->socket, 65535);
    290▕         $read = @fread($this->socket, 8192);
    291▕         if ($read === false || (empty($read) && @feof($this->socket))) {
  ➜ 292▕             throw new ClientException('Error reading');
    293▕         }
    294▕         $this->currentProcessingMessage .= $read;
    295▕         // If we have a complete message, then return it. Save the rest for
    296▕         // later.

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/IvrMenu.php:71
      PAMI\Client\Impl\ClientImpl::process()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()


   Illuminate\Database\QueryException 

  SQLSTATE[HY000] [2002] No such file or directory (Connection: mysql, SQL: select * from `users` where `id` in (11, 14))

  at /home/<USER>/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829
    825▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    826▕                 );
    827▕             }
    828▕ 
  ➜ 829▕             throw new QueryException(
    830▕                 $this->getName(), $query, $this->prepareBindings($bindings), $e
    831▕             );
    832▕         }
    833▕     }

  1   [internal]:0
      App\Console\Commands\CheckAgentReadyStatus::__construct()
      [2m+22 vendor frames [22m

  24  /home/<USER>/app/Console/Commands/CheckAgentReadyStatus.php:91
      Illuminate\Database\Eloquent\Builder::get()


   Illuminate\Database\QueryException 

  SQLSTATE[HY000] [2002] No such file or directory (Connection: mysql, SQL: select * from `users` where `id` in (11, 14))

  at /home/<USER>/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829
    825▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    826▕                 );
    827▕             }
    828▕ 
  ➜ 829▕             throw new QueryException(
    830▕                 $this->getName(), $query, $this->prepareBindings($bindings), $e
    831▕             );
    832▕         }
    833▕     }

  1   [internal]:0
      App\Console\Commands\CheckAgentReadyStatus::__construct()
      [2m+22 vendor frames [22m

  24  /home/<USER>/app/Console/Commands/CheckAgentReadyStatus.php:91
      Illuminate\Database\Eloquent\Builder::get()

IVR MENU RUNNING

   PAMI\Client\Exception\ClientException 

  error: "Connection refused" while create socket "tcp://solutionsv3.tclcontactplus.com:5038"

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:200
    196▕             STREAM_CLIENT_CONNECT,
    197▕             $this->context
    198▕         );
    199▕         if ($this->socket === false) {
  ➜ 200▕             throw new ClientException(sprintf('error: "%s" while create socket "%s"', $errstr, $socketUri));
    201▕         }
    202▕         // set socket in block mode
    203▕         if (!stream_set_blocking($this->socket, true)) {
    204▕             throw new ClientException(sprintf('error set block mode on "%s" socket', $socketUri));

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/IvrMenu.php:68
      PAMI\Client\Impl\ClientImpl::open()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()

IVR MENU RUNNING
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU RUNNING
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU RUNNING

   PAMI\Client\Exception\ClientException 

  Error reading

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:292
    288▕         // Read something.
    289▕         //$read = @fread($this->socket, 65535);
    290▕         $read = @fread($this->socket, 8192);
    291▕         if ($read === false || (empty($read) && @feof($this->socket))) {
  ➜ 292▕             throw new ClientException('Error reading');
    293▕         }
    294▕         $this->currentProcessingMessage .= $read;
    295▕         // If we have a complete message, then return it. Save the rest for
    296▕         // later.

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/IvrMenu.php:80
      PAMI\Client\Impl\ClientImpl::process()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()


   Illuminate\Database\QueryException 

  SQLSTATE[HY000] [2002] No such file or directory (Connection: mysql, SQL: select * from `users` where `id` in (11, 14, 16, 6))

  at /home/<USER>/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829
    825▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    826▕                 );
    827▕             }
    828▕ 
  ➜ 829▕             throw new QueryException(
    830▕                 $this->getName(), $query, $this->prepareBindings($bindings), $e
    831▕             );
    832▕         }
    833▕     }

  1   [internal]:0
      App\Console\Commands\CheckAgentReadyStatus::__construct()
      [2m+22 vendor frames [22m

  24  /home/<USER>/app/Console/Commands/CheckAgentReadyStatus.php:91
      Illuminate\Database\Eloquent\Builder::get()

IVR MENU RUNNING

   PAMI\Client\Exception\ClientException 

  error: "Connection refused" while create socket "tcp://solutionsv3.tclcontactplus.com:5038"

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:200
    196▕             STREAM_CLIENT_CONNECT,
    197▕             $this->context
    198▕         );
    199▕         if ($this->socket === false) {
  ➜ 200▕             throw new ClientException(sprintf('error: "%s" while create socket "%s"', $errstr, $socketUri));
    201▕         }
    202▕         // set socket in block mode
    203▕         if (!stream_set_blocking($this->socket, true)) {
    204▕             throw new ClientException(sprintf('error set block mode on "%s" socket', $socketUri));

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/IvrMenu.php:77
      PAMI\Client\Impl\ClientImpl::open()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()

IVR MENU RUNNING

   PAMI\Client\Exception\ClientException 

  Error reading

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:292
    288▕         // Read something.
    289▕         //$read = @fread($this->socket, 65535);
    290▕         $read = @fread($this->socket, 8192);
    291▕         if ($read === false || (empty($read) && @feof($this->socket))) {
  ➜ 292▕             throw new ClientException('Error reading');
    293▕         }
    294▕         $this->currentProcessingMessage .= $read;
    295▕         // If we have a complete message, then return it. Save the rest for
    296▕         // later.

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/IvrMenu.php:80
      PAMI\Client\Impl\ClientImpl::process()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()

IVR MENU RUNNING

   PAMI\Client\Exception\ClientException 

  error: "Connection refused" while create socket "tcp://solutionsv3.tclcontactplus.com:5038"

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:200
    196▕             STREAM_CLIENT_CONNECT,
    197▕             $this->context
    198▕         );
    199▕         if ($this->socket === false) {
  ➜ 200▕             throw new ClientException(sprintf('error: "%s" while create socket "%s"', $errstr, $socketUri));
    201▕         }
    202▕         // set socket in block mode
    203▕         if (!stream_set_blocking($this->socket, true)) {
    204▕             throw new ClientException(sprintf('error set block mode on "%s" socket', $socketUri));

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/IvrMenu.php:77
      PAMI\Client\Impl\ClientImpl::open()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()

IVR MENU RUNNING
IVR MENU RUNNING

   PAMI\Client\Exception\ClientException 

  error: "Connection refused" while create socket "tcp://solutionsv3.tclcontactplus.com:5038"

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:200
    196▕             STREAM_CLIENT_CONNECT,
    197▕             $this->context
    198▕         );
    199▕         if ($this->socket === false) {
  ➜ 200▕             throw new ClientException(sprintf('error: "%s" while create socket "%s"', $errstr, $socketUri));
    201▕         }
    202▕         // set socket in block mode
    203▕         if (!stream_set_blocking($this->socket, true)) {
    204▕             throw new ClientException(sprintf('error set block mode on "%s" socket', $socketUri));

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/IvrMenu.php:77
      PAMI\Client\Impl\ClientImpl::open()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()

IVR MENU RUNNING

   PAMI\Client\Exception\ClientException 

  error: "Connection refused" while create socket "tcp://solutionsv3.tclcontactplus.com:5038"

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:200
    196▕             STREAM_CLIENT_CONNECT,
    197▕             $this->context
    198▕         );
    199▕         if ($this->socket === false) {
  ➜ 200▕             throw new ClientException(sprintf('error: "%s" while create socket "%s"', $errstr, $socketUri));
    201▕         }
    202▕         // set socket in block mode
    203▕         if (!stream_set_blocking($this->socket, true)) {
    204▕             throw new ClientException(sprintf('error set block mode on "%s" socket', $socketUri));

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/IvrMenu.php:77
      PAMI\Client\Impl\ClientImpl::open()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()

IVR MENU RUNNING

   PAMI\Client\Exception\ClientException 

  error: "Connection refused" while create socket "tcp://solutionsv3.tclcontactplus.com:5038"

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:200
    196▕             STREAM_CLIENT_CONNECT,
    197▕             $this->context
    198▕         );
    199▕         if ($this->socket === false) {
  ➜ 200▕             throw new ClientException(sprintf('error: "%s" while create socket "%s"', $errstr, $socketUri));
    201▕         }
    202▕         // set socket in block mode
    203▕         if (!stream_set_blocking($this->socket, true)) {
    204▕             throw new ClientException(sprintf('error set block mode on "%s" socket', $socketUri));

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/IvrMenu.php:77
      PAMI\Client\Impl\ClientImpl::open()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()

IVR MENU RUNNING

   PAMI\Client\Exception\ClientException 

  error: "Connection refused" while create socket "tcp://solutionsv3.tclcontactplus.com:5038"

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:200
    196▕             STREAM_CLIENT_CONNECT,
    197▕             $this->context
    198▕         );
    199▕         if ($this->socket === false) {
  ➜ 200▕             throw new ClientException(sprintf('error: "%s" while create socket "%s"', $errstr, $socketUri));
    201▕         }
    202▕         // set socket in block mode
    203▕         if (!stream_set_blocking($this->socket, true)) {
    204▕             throw new ClientException(sprintf('error set block mode on "%s" socket', $socketUri));

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/IvrMenu.php:77
      PAMI\Client\Impl\ClientImpl::open()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()

IVR MENU RUNNING

   PAMI\Client\Exception\ClientException 

  error: "Connection refused" while create socket "tcp://solutionsv3.tclcontactplus.com:5038"

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:200
    196▕             STREAM_CLIENT_CONNECT,
    197▕             $this->context
    198▕         );
    199▕         if ($this->socket === false) {
  ➜ 200▕             throw new ClientException(sprintf('error: "%s" while create socket "%s"', $errstr, $socketUri));
    201▕         }
    202▕         // set socket in block mode
    203▕         if (!stream_set_blocking($this->socket, true)) {
    204▕             throw new ClientException(sprintf('error set block mode on "%s" socket', $socketUri));

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/IvrMenu.php:77
      PAMI\Client\Impl\ClientImpl::open()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()

IVR MENU RUNNING
ARG : agi_request: async
agi_channel: PJSIP/HostedFET238-********
agi_language: en
agi_type: PJSIP
agi_uniqueid: **********.0
agi_version: 22.3.0
agi_callerid: ***********
agi_calleridname: +************
agi_callingpres: 0
agi_callingani2: 0
agi_callington: 0
agi_callingtns: 0
agi_dnid: **********
agi_rdnis: unknown
agi_context: default
agi_extension: **********
agi_priority: 4
agi_enhanced: 0.0
agi_accountcode: 
agi_threadid: ***************
agi_arg_1: IvrMenu


ARG_VALUE : IvrMenu
IVR MENU Processing
Timing Res : 1
No priority menu found, running normal flow.
IVR MENU RUNNING
IVR MENU RUNNING
IVR MENU RUNNING
IVR MENU RUNNING
IVR MENU RUNNING
IVR MENU RUNNING

   PAMI\Client\Exception\ClientException 

  Error reading

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:292
    288▕         // Read something.
    289▕         //$read = @fread($this->socket, 65535);
    290▕         $read = @fread($this->socket, 8192);
    291▕         if ($read === false || (empty($read) && @feof($this->socket))) {
  ➜ 292▕             throw new ClientException('Error reading');
    293▕         }
    294▕         $this->currentProcessingMessage .= $read;
    295▕         // If we have a complete message, then return it. Save the rest for
    296▕         // later.

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/IvrMenu.php:80
      PAMI\Client\Impl\ClientImpl::process()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()

IVR MENU RUNNING

   PAMI\Client\Exception\ClientException 

  error: "Connection refused" while create socket "tcp://127.0.0.1:5038"

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:200
    196▕             STREAM_CLIENT_CONNECT,
    197▕             $this->context
    198▕         );
    199▕         if ($this->socket === false) {
  ➜ 200▕             throw new ClientException(sprintf('error: "%s" while create socket "%s"', $errstr, $socketUri));
    201▕         }
    202▕         // set socket in block mode
    203▕         if (!stream_set_blocking($this->socket, true)) {
    204▕             throw new ClientException(sprintf('error set block mode on "%s" socket', $socketUri));

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/IvrMenu.php:77
      PAMI\Client\Impl\ClientImpl::open()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()

IVR MENU RUNNING

   PAMI\Client\Exception\ClientException 

  error: "Connection refused" while create socket "tcp://127.0.0.1:5038"

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:200
    196▕             STREAM_CLIENT_CONNECT,
    197▕             $this->context
    198▕         );
    199▕         if ($this->socket === false) {
  ➜ 200▕             throw new ClientException(sprintf('error: "%s" while create socket "%s"', $errstr, $socketUri));
    201▕         }
    202▕         // set socket in block mode
    203▕         if (!stream_set_blocking($this->socket, true)) {
    204▕             throw new ClientException(sprintf('error set block mode on "%s" socket', $socketUri));

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/IvrMenu.php:77
      PAMI\Client\Impl\ClientImpl::open()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()

IVR MENU RUNNING

   PAMI\Client\Exception\ClientException 

  error: "Connection refused" while create socket "tcp://127.0.0.1:5038"

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:200
    196▕             STREAM_CLIENT_CONNECT,
    197▕             $this->context
    198▕         );
    199▕         if ($this->socket === false) {
  ➜ 200▕             throw new ClientException(sprintf('error: "%s" while create socket "%s"', $errstr, $socketUri));
    201▕         }
    202▕         // set socket in block mode
    203▕         if (!stream_set_blocking($this->socket, true)) {
    204▕             throw new ClientException(sprintf('error set block mode on "%s" socket', $socketUri));

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/IvrMenu.php:77
      PAMI\Client\Impl\ClientImpl::open()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()

IVR MENU RUNNING

   PAMI\Client\Exception\ClientException 

  error: "Connection refused" while create socket "tcp://127.0.0.1:5038"

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:200
    196▕             STREAM_CLIENT_CONNECT,
    197▕             $this->context
    198▕         );
    199▕         if ($this->socket === false) {
  ➜ 200▕             throw new ClientException(sprintf('error: "%s" while create socket "%s"', $errstr, $socketUri));
    201▕         }
    202▕         // set socket in block mode
    203▕         if (!stream_set_blocking($this->socket, true)) {
    204▕             throw new ClientException(sprintf('error set block mode on "%s" socket', $socketUri));

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/IvrMenu.php:77
      PAMI\Client\Impl\ClientImpl::open()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()

