<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use STS\ZipStream\ZipStream;
use Zip;
use ZipArchive;

class BulkRecordingDownloadController extends Controller
{
    function dateRange($first, $last, $step = '+1 day', $format = 'Y-m-d'): array
    {
        $dates = [];
        $current = strtotime( $first );
        $last = strtotime( $last );
        while( $current <= $last ) {
            $dates[] = date( $format, $current );
            $current = strtotime( $step, $current );
        }
        return $dates;
    }

    public function getBulkData(Request $request)
    {
        if(isset($request->date)) {
                // $date1 = Carbon::parse($request->date[0])->format('Y-m-d');
                // $date2 = Carbon::parse($request->date[1])->format('Y-m-d');
    
                // $period = $this->dateRange($date1, $date2);
                // $maxArchiveSize = 3 * 1024 * 1024 * 1024; // 3GB in bytes
                // $zipCount = 2;
                // $directory = 'recordings';
                // //$zip = new Zip("recordings_$zipCount.zip");
               
                // foreach ($period as $key => $date) {
                //     $totalSize = 0;
                //     $zip = Zip::create("recordings_$zipCount.zip");
                //     $current = Carbon::parse($date);
                //     $year = $current->format('Y');
                //     $month = $current->format('m');
                //     $day = $current->format('d');
                //     $files = Storage::disk('recordings')->files("/{$year}/{$month}/{$day}");
                //     $totalElements = count($files);
                //     foreach($files as $index => $file) {
                //         $filePath = Storage::disk('recordings')->path($file);
                //         $fileSize = filesize($filePath);
    
                //         if ($totalSize + $fileSize > $maxArchiveSize) {
                //             $zip->saveTo('storage/recordings');
                //             $zipCount++;
                //             $totalSize = 0;
                //             //$zip = new Zip("recordings_$zipCount.zip");
                //             $zip = Zip::create("recordings_$zipCount.zip");
                //         }
                       
                //         $zip->add($filePath);
                //         $totalSize += $fileSize;
    
                //         if($index == ($totalElements - 1)) {
                //             $zip->saveTo('storage/recordings');
                //             $zipCount++;
                //         }
                //     }
                // }
    
            $date = $request->date;
            $current = Carbon::parse($date);
            $year = $current->format('Y');
            $month = $current->format('m');
            $day = $current->format('d');
            $files = Storage::disk('recordings')->files("/{$year}/{$month}/{$day}");

            /*
            $zip = Zip::create("recordings.zip");
            foreach($files as $file) {
                $zip->add(Storage::disk('recordings')->path($file));
            }
            return $zip;
            */

            if (empty($files)) {
                return response()->json(['message' => 'No recordings found for the given date.'], 404);
            }

            $zip = new ZipArchive();
            $zipPath = storage_path('app/recordings.zip');

            if ($zip->open($zipPath, ZipArchive::CREATE | ZipArchive::OVERWRITE) !== true) {
                return response()->json("Failed to create ZIP file.", 500);
            }

            foreach ($files as $file) {
                $zip->addFile(
                    Storage::disk('recordings')->path($file),
                    basename($file)
                );
            }

            $zip->close();

            return response()->download($zipPath)->deleteFileAfterSend(true);    
        } else {
            return response()->json(['message' => 'Invalid datetime format.'], 404);
        }
    }
}