<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Symfony\Component\Process\Process;

class RestartIntegrationWorker extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'restart:integration';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'this will restart the supervisorctl process integration-worker:*';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $process = new Process(['supervisorctl', 'restart integration-worker:*']);
        $process->run();

        $this->info("Command restarted successfully and the output is : {$process->getOutput()}");
        //$this->info($process->getOutput());
    }
}
