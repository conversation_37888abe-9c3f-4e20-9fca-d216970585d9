<?php

use App\Http\Controllers\AgentLoginReportController;
use App\Http\Controllers\AGIController;
use App\Http\Controllers\BreakReportController;
use App\Http\Controllers\BulkRecordingDownloadController;
use App\Http\Controllers\CallDetailRecordController;
use App\Http\Controllers\ChannelOccupancyController;
use App\Http\Controllers\FormDataReportController;
use App\Http\Controllers\CustomNumberController;
use App\Http\Controllers\InboundDispositionController;
use App\Http\Controllers\InboundDispositionSummaryController;
use App\Http\Controllers\MenuController;
use App\Http\Controllers\MenuOptionController;
use App\Http\Controllers\IvrMenuSettingController;
use App\Http\Controllers\OutboundActivityController;
use App\Http\Controllers\OutboundAgentSummaryController;
use App\Http\Controllers\OutboundDashboardController;
use App\Http\Controllers\OutboundDispositionController;
use App\Http\Controllers\OutboundDispositionSummaryController;
use App\Http\Controllers\AgentCallReportController;
use App\Http\Controllers\AgentController;
use App\Http\Controllers\AgentDashboardController;
use App\Http\Controllers\AgentlessCampaignController;
use App\Http\Controllers\AgentlessTestController;
use App\Http\Controllers\AudioController;
use App\Http\Controllers\CallApiController;
use App\Http\Controllers\CallStatusController;
use App\Http\Controllers\CampaignController;
use App\Http\Controllers\CampaignLogController;
use App\Http\Controllers\CampaignNumberController;
use App\Http\Controllers\CampaignUserController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ExtensionController;
use App\Http\Controllers\FormController;
use App\Http\Controllers\FormFieldController;
use App\Http\Controllers\FormFieldTypeController;
use App\Http\Controllers\InboundController;
use App\Http\Controllers\IVRController;
use App\Http\Controllers\OutboundController;
use App\Http\Controllers\OutboundWorkcodeController;
use App\Http\Controllers\PauseReasonController;
use App\Http\Controllers\PersonController;
use App\Http\Controllers\QueueController;
use App\Http\Controllers\RecordingController;
use App\Http\Controllers\RingNoAnswerAgentController;
use App\Http\Controllers\RingNoAnswerQueueController;
use App\Http\Controllers\RingNoAnswerReportController;
use App\Http\Controllers\ScriptController;
use App\Http\Controllers\ServiceRatingController;
use App\Http\Controllers\SystemSettingController;
use App\Http\Controllers\SystemStatsController;
use App\Http\Controllers\UserAgentController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\PermissionController;
use App\Http\Controllers\VoicemailSettingController;
use App\Http\Controllers\WorkCodeController;
use App\Http\Controllers\OutboundlistController;
use App\Http\Controllers\FileController;
use App\Http\Controllers\IntegrationController;
use App\Http\Controllers\InboundAgentSummaryController;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\CdrController;
use App\Http\Controllers\AgnetReportController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\CallPerAgentController;
use App\Http\Controllers\StatsController;
use App\Http\Controllers\CallController;
use App\Http\Controllers\CustomReportController;
use App\Http\Controllers\FormDataController;
use App\Http\Controllers\FormFunctionController;
use App\Models\Cdr;
use App\Models\User;
use App\Http\Middleware\RemoveQueryParamsFromRequest;
use App\Http\Controllers\ScheduleCallsController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\AspireCustomFormDataController;
use App\Http\Controllers\CallingServerController;
use App\Http\Controllers\IvrSettingController;
use App\Http\Controllers\UserLimitController;
use App\Http\Controllers\NavbarController;
use App\Http\Controllers\PrepaidController;
use Illuminate\Support\Facades\Broadcast;
use App\Http\Controllers\ConversationController;
use App\Models\Conversation;
use App\Http\Controllers\SMSCategoryController;
use App\Http\Controllers\SMSTemplateController;
use App\Http\Controllers\GmailController;
use App\Http\Controllers\AnnouncementController;
use App\Http\Controllers\CallbackRequestController;
use App\Http\Controllers\ModuleAccessibilityController;
use App\Http\Controllers\DTMFSettingController;
use App\Http\Controllers\EmailController;
use App\Http\Controllers\MnPReportsController;
use App\Http\Controllers\BroacatEventsController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/


Broadcast::routes();

Route::middleware([RemoveQueryParamsFromRequest::class])->group(function () {

    Route::get('/menu-items', function (Request $request) {
        $modulesPermissoins = \App\Models\Permission::query()->where('name', 'like', 'read_%')->get(['name']);
        $data = \App\Models\User::whereHasPermission($modulesPermissoins)->get();
        return User::auth();
    });


    //Route::get('/menu-items', function (Request $request) {
    //    return [
    //        'Dashboard',
    //        'User',
    //        'Agent',
    //        'Queue',
    //        'Visual IVR',
    //        'Work Code',
    //        'Paused Reason',
    //        'Media Files',
    //        'Inbound Routes',
    //        'Outbound Routes',
    //        'Settings',
    //        'Role',
    //        'Permission',
    //        'Reports' => [
    //            'agentReport',
    //            'Call Detail Report',
    //            'PauseReasonReport',
    //            'CallPerAgentController',
    //            'CompleteAgent'
    //        ]
    //    ];
    //});


    Route::get('/getModules', function (Request $request) {
        // when create permission module use please underscore b/w the words
        return [
            'users',
            'agents',
            'queuesl',
            'visualivr',
            'workcodes',
            'pausedreason',
            'audio',
            'inbounds',
            'outbounds',
            'settings',
            'roles',
            'permissions',
            'agentmonitoring',
            'dashboard',
            'supervisors',
            'campaigns',
            'cid-lookup',
            'forms',
            'scripts',
            'breaks',
            'voicemails',
            'greetease',
            'service-rating',
            'dtmf-settings',
            'bitrix',
            'prepaidSetting',
            'ticker',
            'emailSetting',
            'sms',
            'announcement',
            'reports',
            'agentless-campaign',
            'agentless-calling-server',
            'agentless-recording',
            'agentless-campaign'
        ];
    });




    //Route::post("/login", [\App\Http\Controllers\AuthController::class, 'login']);
    Route::post('/{campaign}/upload', [CampaignNumberController::class, 'store']);
    Route::post('/test/upload', [CampaignNumberController::class, 'test']);
    Route::get('exportCustomFormData', [AspireCustomFormDataController::class, 'exportCustomFormData']);
    Route::get('cdrExport', [CallDetailRecordController::class, 'cdrExport']);
    Route::get('cdrExportFilter', [CallDetailRecordController::class, 'cdrExportFilter']);
    Route::get('exportFormData', [FormDataController::class, 'export']);
    Route::get('/{campaign}/campaignNumberExport', [CampaignNumberController::class, 'campaignNumberExport']);
    Route::get('/ring-no-answer/export', [RingNoAnswerReportController::class, 'export']);
    Route::get('abandonCallExport', [CallDetailRecordController::class, 'abondonCallDataExport']);


    Route::post('/login', [AuthController::class, 'login']);

    Route::post('/agent-login', [AuthController::class, 'LoginAgent']);
    Route::post("/supervisorLogin", [AuthController::class, 'supervisorLogin']);
    Route::get('/dialCall', [CallController::class, 'dispatchCall']);
    Route::get('/voicemail/{voicemail}', [VoicemailSettingController::class, 'getVoicemailAudio']);
    Route::get('/getcallback-voicemail/{voicemail}', [CallbackRequestController::class, 'getCallbackVoicemailAudio']);

    Route::get('/navbar', [NavbarController::class, 'index']);

    Route::post('/announcements/submit', [AnnouncementController::class, 'store'])->middleware('auth:sanctum');

    Route::group(['middleware' => ['auth:sanctum']], function () {

        Route::get('/broadcast-events', [BroacatEventsController::class, 'broadcast']);

        // CallBackRequest Routes
        Route::get('/announcement-list', [AnnouncementController::class, 'announcementList']);
        Route::delete('/announcement/{id}', [AnnouncementController::class, 'announcementDelete']);
        Route::get('/callback-list', [CallbackRequestController::class, 'callbackList']);
        Route::get('/callback-pending-list', [CallbackRequestController::class, 'pendingCallbackList']);
        Route::patch('/callback-requests/{id}/status', [CallbackRequestController::class, 'updateStatus']);

        Route::post('/update-pagination', function (Request $request) {
            $userId = auth()->id();
            $table = $request->input('table');
            $users = Cache::get('logged_in_users', []);

            $users[$userId]['pagination'][$table] = [
                'current' => $request->input('current', 1),
                'pageSize' => $request->input('pageSize', 15)
            ];

            Cache::put('logged_in_users', $users);

            return response()->json(['success' => true]);
        });

        //SMS MODULE
        Route::resource('sMSCategory', SMSCategoryController::class);
        Route::resource('sMSTemplate', SMSTemplateController::class);
        Route::post('send-sms', [SMSTemplateController::class, 'sendSMS']);

        Route::group(['prefix' => 'conversation'], function () {
            Route::post('/create/{id}', [ConversationController::class, 'createRoom']);
            Route::get('/allUsers', [ConversationController::class, 'getAllUsers']);
            Route::get('/convUsers', [ConversationController::class, 'conversationList']);
            Route::get('/chatroom/{id}', [ConversationController::class, 'chatRoom']);
            Route::post('/send', [ConversationController::class, 'sendMessage']);
            Route::post('/chat/delete', [ConversationController::class, 'deleteConversation']);
            Route::post('delivered/{id}', \App\Http\Controllers\MessageDelieveredController::class);
            Route::get('/messages/unread-count', [ConversationController::class, 'getUnreadMessageCount']);
            Route::post('/messages/mark-as-read', [ConversationController::class, 'markAsRead']);

            Route::get('/test', [ConversationController::class, 'test']);
        });

        Route::post('/bulkAgentImport', [UserAgentController::class, 'bulkAgentImport']);
        Route::get('/bitrix', [IntegrationController::class, 'index']);
        Route::put('/bitrix/{id}', [IntegrationController::class, 'update']);

        Route::post('/logout', [AuthController::class, 'logoutUser']);

        Route::get('/get-user', function (Request $request) {
            // return $request->user();

            $user = $request->user()->load('roles');
            return $user;
        });


        Route::get('/sms-accessibility', [ModuleAccessibilityController::class, 'index']);
        Route::put('/sms-accessibility', [ModuleAccessibilityController::class, 'update']);

        // Email 
        Route::get('/email-accessibility', [ModuleAccessibilityController::class, 'getEmail']);
        Route::put('/email-accessibility', [ModuleAccessibilityController::class, 'updateEmail']);

        Route::get('/allowed_modules', [NavbarController::class, 'getAllowedNavbarModules']);

        // Service Rating Routes
        Route::post('/serviceRating', [AGIController::class, 'getServiceRating']);
        Route::get('/getServiceRatings', [ServiceRatingController::class, 'getRatings']);
        Route::get('/getServiceRatingSettings', [ServiceRatingController::class, 'getSettings']);
        Route::get('/getServiceRatingFiles', [ServiceRatingController::class, 'getFiles']);
        Route::post('/uploadServiceRatingFile', [ServiceRatingController::class, 'uploadFile']);
        Route::patch('/updateServiceRatingSettings', [ServiceRatingController::class, 'updateSettings']);

        // Voicemail Routes
        Route::get('/exportVoicemail', [VoicemailSettingController::class, 'exportVoicemail']);
        Route::get('/voicemail', [VoicemailSettingController::class, 'getVoicemails']);
        Route::get('/getVoiceMailSettings', [VoicemailSettingController::class, 'getSettings']);
        Route::get('/getVoiceMailFiles', [VoicemailSettingController::class, 'getVoiceMailFiles']);
        Route::post('/uploadVoiceMailFile', [VoicemailSettingController::class, 'uploadFile']);
        Route::patch('/updateVoiceMailSettings', [VoicemailSettingController::class, 'updateSettings']);


        // IVR Routes
        Route::get('/ivr', [IvrSettingController::class, 'getIVRS']);
        Route::get('/getIvrSettings', [IvrSettingController::class, 'getSettings']);
        Route::get('/getIvrFiles', [IvrSettingController::class, 'getIvrFiles']);
        Route::post('/uploadIvrFile', [IvrSettingController::class, 'uploadFile']);
        Route::patch('/updateIvrSettings', [IvrSettingController::class, 'updateSettings']);

        // No of User Limit
        Route::get('/user-limit', [UserLimitController::class, 'index']);
        Route::patch('/user-limit', [UserLimitController::class, 'update']);

        // dashboard route
        Route::get("/getStats", [StatsController::class, 'getStats']);

        //Route::resource('/menu-item', \App\Http\Controllers\MenuItemController::class);

        //Meeting Reports
        Route::get('cdr/minutesOfMeeting', [CdrController::class, 'minutesOfMeeting']);
        Route::post('cdr/minutesOfMeeting/filtered', [CdrController::class, 'minutesOfMeetingFiltered']);

        //agentCallSummary Inbound
        // Route::get('agent/agentCallSummaryInbound', [AgentCallReportController::class, 'agentCallSummaryInbound']);
        // //agentCallSummary Inbound Filter
        // Route::post('agent/agentCallSummaryInbound/filtered', [AgentCallReportController::class, 'agentCallSummaryFilteredInbound']);


        Route::get('agent/agentCallSummary', [AgentCallReportController::class, 'agentCallSummary']);
        Route::post('agent/agentCallSummary/filtered', [AgentCallReportController::class, 'agentCallSummaryFiltered']);


        //Route::resource('/menu-item', \App\Http\Controllers\MenuItemController::class);

        //Meeting Reports
        Route::get('cdr/minutesOfMeeting', [CdrController::class, 'minutesOfMeeting']);
        Route::post('cdr/minutesOfMeeting/filtered', [CdrController::class, 'minutesOfMeetingFiltered']);

        //agentCallSummary Inbound
        Route::get('agent/agentCallSummaryInbound', [AgentCallReportController::class, 'agentCallSummaryInbound']);
        //agentCallSummary Inbound Filter
        Route::post('agent/agentCallSummaryInbound/filtered', [AgentCallReportController::class, 'agentCallSummaryFilteredInbound']);


        Route::get('agent/agentCallSummary', [AgentCallReportController::class, 'agentCallSummary']);
        Route::post('agent/agentCallSummary/filtered', [AgentCallReportController::class, 'agentCallSummaryFiltered']);


        Route::get('/cdr/report', function (Request $request) {
            $paginateReport = Cdr::query()->orderBy('end', 'desc')->paginate($request->pageSize);
            $data = Cdr::query()->whereMonth('start', Carbon::now()->month)->get();
            return response()->json(['paginateReport' => $paginateReport, 'data' => $data]);
        });

        Route::resource('queue', QueueController::class, ['names' => ['update' => 'queue/{name}']]);
        Route::resource('inbound', InboundController::class);
        Route::resource('outbound', OutboundController::class);
        Route::resource('extension', ExtensionController::class);
        Route::resource('person', PersonController::class);
        // Route::resource('system-setting', SystemSettingController::class)->only(['view','index', 'update']);
        Route::resource('system-setting', SystemSettingController::class);
        Route::resource('workCode', WorkCodeController::class);
        Route::resource('pause-reason', PauseReasonController::class);
        Route::resource('user', UserController::class);
        Route::post('changePassword', [UserController::class, 'changePassword']);
        Route::resource('ivr', IVRController::class);
        Route::resource('outboundWorkCode', OutboundWorkcodeController::class)->only('store');
        Route::resource('audio', AudioController::class)->only(['index', 'store', 'destroy']);
        Route::resource('cdr', CdrController::class);
        Route::resource('campaign', CampaignController::class);
        Route::resource('form', FormController::class);
        Route::resource('script', ScriptController::class);
        Route::put('/scriptUpdate/{script}', [ScriptController::class, 'updateStatus']);
        Route::resource('formFieldType', FormFieldTypeController::class);
        Route::resource('/{form}/formField', FormFieldController::class);
        Route::post('/{form}/formFieldOptionUpdate', [FormFieldController::class, 'optionUpdate']);
        Route::resource('callStatus', CallStatusController::class)->only('index');
        Route::resource('/{campaign}/campaignNumber', CampaignNumberController::class);
        Route::resource('/{campaign}/customNumber', CustomNumberController::class);
        Route::post('formFunction', [FormFunctionController::class, 'store']);
        Route::delete('formFunction/{formFunction}', [FormFunctionController::class, 'destroy']);
        Route::get('functionType', [FormFunctionController::class, 'functionType']);
        Route::get('referenceField', [FormFunctionController::class, 'referenceField']);
        Route::post('/{campaign}/filterCampaignNumber', [CampaignNumberController::class, 'getFilteredCampaignNumber']);
        Route::get('/campaignSetting', [CampaignController::class, 'campaignSetting']);
        Route::put('/campaignSetting/{campaignSetting}', [CampaignController::class, 'updateCampaignSetting']);

        //GET Script BY QUEUE
        Route::post('get-script-by-queue', [ScriptController::class, 'getScriptByQueue']);

        // Accountcode routes
        Route::get('/accountCodes', [CallStatusController::class, 'getAccountCodes']);

        // Get number from campaign
        Route::get('/{campaign}/number', [CampaignNumberController::class, 'fetch']);
        Route::get('/{campaign}/fetchCustomNumber', [CustomNumberController::class, 'fetch']);
        Route::post('/{campaign}/{campaignNumber}/update', [CampaignNumberController::class, 'updateState']);
        Route::post('/{campaign}/{customNumber}/updateCustomNumber', [CustomNumberController::class, 'updateState']);

        // Add user in campaign
        Route::get('/{campaign}/campaignUser', [CampaignUserController::class, 'index']);
        Route::get('/{user}/campaign/user', [CampaignUserController::class, 'get_campaign']);
        Route::post('/{campaign}/{user}/campaignUser', [CampaignUserController::class, 'add_user']);
        Route::post('/{campaign}/campaignUsers', [CampaignUserController::class, 'add_users']);
        Route::delete('/{campaign}/{user}/campaignUser', [CampaignUserController::class, 'destroy']);

        // Campaign log routes
        Route::resource('campaignLog', CampaignLogController::class)->only(['store', 'index']);

        Route::resource('userAgent', UserAgentController::class)->parameter('userAgent', 'user');
        Route::post('/{user}/agent/password/reset', [AuthController::class, 'passwordReset']);
        Route::resource('role', RoleController::class);
        Route::resource('permission', PermissionController::class);
        Route::get('getpermissions ', [PermissionController::class, 'getpermissionsInRole']);
        Route::post('/check-user-authentic', [AuthController::class, 'isAuthenticated']);

        //CDR report Filter
        Route::post('cdr/CdrReportFilter', [CdrController::class, 'CdrReportFilter']);
        Route::post('cdr/CdrReportFilter', [CdrController::class, 'CdrReportFilter']);
        Route::get('cdr/minutesOfMeeting', [CdrController::class, 'minutesOfMeeting']);


        //AgentReport
        //if request in post  (get filtered Agent Login Report data)
        Route::resource('agent/report', AgnetReportController::class);

        //get PauseReason Report
        Route::post('agent/pauseReasonReport', [AgnetReportController::class, 'getPauseReasonReport']);
        Route::post('agent/pauseReasonReportFilter', [AgnetReportController::class, 'getPauseReasonReportFilter']);

        // get Detail RING NO ANSWER
        Route::post('agent/ringNoAnswer', [AgnetReportController::class, 'ringNoAnswer']);
        Route::post('agent/ringNoAnswerFilter', [AgnetReportController::class, 'ringNoAnswerFilter']);

        //get Detail EXIT EMPTY
        Route::get('agent/exitEmpty', [AgnetReportController::class, 'exitEmpty']);
        Route::post('agent/exitEmptyFilter', [AgnetReportController::class, 'exitEmptyFilter']);

        //get Detail EXIT WITH TIMEOUT
        Route::get('agent/exitTimeout', [AgnetReportController::class, 'exitWithTimeout']);
        Route::post('agent/exitTimeoutFilter', [AgnetReportController::class, 'exitWithTimeoutFilter']);

        //get Detail Configured Load FIlter
        Route::get('agent/exitTimeout', [AgnetReportController::class, 'exitEmpty']);
        Route::post('agent/exitTimeoutFilter', [AgnetReportController::class, 'exitEmptyFilter']);

        //get Detail Complete Agent
        Route::get('agent/completeAgent', [AgnetReportController::class, 'completeAgent']);
        Route::post('agent/completeAgentFilter', [AgnetReportController::class, 'completeAgentFilter']);

        // Report Call per agent
        Route::get('agent/callPerAgent', [AgnetReportController::class, 'getCallPerAgent']);
        Route::post('agent/callPerAgentFilter', [AgnetReportController::class, 'getCallPerAgentFilter']);

        //Report Caller Hangup
        Route::get('agent/configuredLoad', [AgnetReportController::class, 'configuredLoad']);
        Route::post('agent/configuredLoadFilter', [AgnetReportController::class, 'configuredLoadFilter']);

        //Report Complete Caller
        Route::get('agent/completeCaller', [AgnetReportController::class, 'completeCaller']);
        Route::post('agent/completeCallerFilter', [AgnetReportController::class, 'completeCallerFilter']);

        //Supervisor
        Route::resource('supervisor', \App\Http\Controllers\SupervisorController::class)->parameter('supervisor', 'user');

        // permissions
        Route::post('assign/permission', [RoleController::class, 'assignPermission']);
        Route::post('assign/getPermissionByRole', [RoleController::class, 'getPermissionByRole']);
        Route::post('getAllUsers', [UserController::class, 'getAllUsers']);
        Route::post('assign/roleAssignToUser', [RoleController::class, 'assignRoleToUser']);
        Route::post('getRoleAssignToUser', [RoleController::class, 'getRoleAssignToUser']);

        // System stats
        Route::post('stats', [SystemStatsController::class, 'index']);

        //Get Settings for Agent
        Route::get('getSettings', [SystemSettingController::class, 'getSettings']);
        Route::get('getAutoCall', [SystemSettingController::class, 'getAutoCall']);

        //SMS MODULE
        Route::resource('sMSCategory', \App\Http\Controllers\SMSCategoryController::class);
        Route::resource('sMSTemplate', \App\Http\Controllers\SMSTemplateController::class);
        Route::post('send-sms', [\App\Http\Controllers\SMSTemplateController::class, 'sendSMS']);

        // IVR MENU AND OPTIONS
        Route::apiResource('menus', MenuController::class);
        Route::get('/get-all-menus', [MenuController::class, 'getAllMenu']);
        Route::put('/menus/{id}/priority', [MenuController::class, 'updatePriority']);
        Route::put('/menus/{id}/off_working', [MenuController::class, 'updateOffWorking']);
        Route::post('/menu-options/{id}', [MenuOptionController::class, 'updateMenuOption']);
        Route::post('/menu-media-upload/{id}', [MenuOptionController::class, 'updateMenuMediaFile']);
        Route::apiResource('menu-options', MenuOptionController::class);
        Route::patch('/update-ivr-menu-settings', [IvrMenuSettingController::class, 'updateSettings']);
        Route::get('/get-ivr-menu-settings', [IvrMenuSettingController::class, 'getMenuSettings']);

        Route::get('getAgents ', [MnPReportsController::class, 'getAgents']);
        Route::post('/getAgentsForFilter', [InboundDispositionSummaryController::class, 'getAgentsForFilter']);

        Route::group(['prefix' => 'report'], function () {
            Route::post('agent-call', [AgentCallReportController::class, 'index']);
            Route::post('agent-login', [AgentLoginReportController::class, 'getAgentLoginReport']);
            Route::post('agent-call-filtered', [AgentCallReportController::class, 'getFilteredReport']);
            Route::post('outbound-disposition', [OutboundDispositionController::class, 'getOutboundDisposition']);
            Route::post('outbound-disposition-filtered', [OutboundDispositionController::class, 'getFilteredOutboundDisposition']);
            Route::post('export-outbound-disposition', [OutboundDispositionController::class, 'exportOutboundDisposition']);
            Route::post('outbound-disposition-summary', [OutboundDispositionSummaryController::class, 'getOutboundDispositionSummary']);
            Route::post('outbound-disposition-summary-filter', [OutboundDispositionSummaryController::class, 'getOutboundDispositionSummary']);
            Route::post('inbound-agent-summary/filter', [InboundAgentSummaryController::class, 'getFilteredInboundAgentSummary']);
            Route::post('inbound-agent-summary', [InboundAgentSummaryController::class, 'getInboundAgentSummary']);
            Route::post('inbound-agent-summary-columns', [InboundAgentSummaryController::class, 'getInboundAgentSummaryColumns']);
            Route::post('inbound-disposition', [InboundDispositionController::class, 'getInboundDisposition']);
            Route::post('inbound-disposition-filtered', [InboundDispositionController::class, 'getFilteredInboundDisposition']);
            Route::post('export-inbound-disposition', [InboundDispositionController::class, 'exportInboundDisposition']);
            Route::post('inbound-disposition-summary', [InboundDispositionSummaryController::class, 'getInboundDispositionSummary']);
            Route::post('inbound-disposition-summary-filter', [InboundDispositionSummaryController::class, 'getInboundDispositionSummary']);
            Route::post('outbound-agent-summary/filter', [OutboundAgentSummaryController::class, 'getFilteredOutboundAgentSummary']);
            Route::post('outbound-agent-summary', [OutboundAgentSummaryController::class, 'getOutboundAgentSummary']);
            Route::post('outbound-agent-summary-columns', [OutboundAgentSummaryController::class, 'getOutboundAgentSummaryColumns']);
            Route::post('outbound-activity', [OutboundActivityController::class, 'getOutboundActivity']);
            Route::post('outbound-activity-filtered', [OutboundActivityController::class, 'getOutboundActivityFiltered']);
            Route::post('call-detail-records', [CallDetailRecordController::class, 'getCallDetailRecords']);
            Route::post('export-abandon-call', [CallDetailRecordController::class, 'exportAbandonCallData']);

            Route::post('call-detail-records-filtered', [CallDetailRecordController::class, 'getCallDetailRecordsFiltered']);
            Route::post('form-data-report', [FormDataReportController::class, 'getFormDataReport']);
            Route::post('form-data-report-filtered', [FormDataReportController::class, 'getFormDataReportFiltered']);
            Route::post('form-data-report-download', [FormDataReportController::class, 'downloadFormDataReport']);
            Route::post('ring-no-answer', [RingNoAnswerReportController::class, 'getData']);
            Route::post('ring-no-answer-agent', [RingNoAnswerAgentController::class, 'getData']);
            Route::post('ring-no-answer-queue', [RingNoAnswerQueueController::class, 'getData']);
            Route::post('channel-occupancy', [ChannelOccupancyController::class, 'getData']);
            Route::post('break-report-columns', [BreakReportController::class, 'getBreakReportColumns']);
            Route::post('break-report-data', [BreakReportController::class, 'getBreakReport']);
            Route::post('get-all-breaks', [BreakReportController::class, 'getAllBreaks']);
            Route::post('call-per-agent', [CallPerAgentController::class, 'getCallPerAgentReport']);
            Route::post('trunk-per-hour', [ChannelOccupancyController::class, 'getTrunkPerHour']);
            Route::post('trunk-per-hour-outbound', [ChannelOccupancyController::class, 'getTrunkPerHourOutbound']);
            Route::post('hold-time', [CallDetailRecordController::class, 'holdTimeReport']);
            Route::post('bulk-recording-download', [BulkRecordingDownloadController::class, 'getBulkData']);
            Route::post('abandon-call-hours', [CallDetailRecordController::class, 'abandonCallDataHours']);
            Route::post('abandon-call', [CallDetailRecordController::class, 'abandonCallData']);
            Route::post('last-10-abandon-call', [CallDetailRecordController::class, 'Last10AbandonCallData']);
            Route::post('abandonCallDifferenceReport', [CallDetailRecordController::class, 'abandonCallDifferenceReport']);

            Route::post('abandonCallReport', [CallDetailRecordController::class, 'abandonCallReport']);
            Route::put('update-abandoncall-status', [CallDetailRecordController::class, 'updateStatus']);

            Route::post('getCallQueueSummaryReport', [CustomReportController::class, 'callQueueSummaryReport']);
            Route::post('getQueueWiseReport', [CustomReportController::class, 'queueWiseReport']);

            Route::post('summaryReport', [CustomReportController::class, 'SummaryReport']);
            Route::post('exportSummaryReport', [CustomReportController::class, 'exportSummaryReport']);

            // Aspire Custom Form Data Routes
            Route::post('aspireCustomFormData', [AspireCustomFormDataController::class, 'index']); //
            Route::get('aspireCustomFormDataFormFilterData', [AspireCustomFormDataController::class, 'getFormfilterModelData']);
            //Route::get('aspireCustomReportFiltered', [AspireCustomFormDataController::class,'getFormDataReportFiltered']);

            Route::get('/getcallback-request', [CallbackRequestController::class, 'getCallbackRequest']);
            Route::post('/export', [CallbackRequestController::class, 'export']);

            // mnp report 
            Route::post('overall-call-handling-metric', [MnPReportsController::class, 'overallCallHandlingMetric']);
            Route::post('getAgentStatus', [MnPReportsController::class, 'agentStatus']);
            Route::post('getCallQueueSummaryReport', [MnPReportsController::class, 'callQueueSummaryReport']);
            Route::post('getHourlyAbandonReport', [MnPReportsController::class, 'hourlyAbandonReport']);
            Route::post('getServiceLevelReport', [MnPReportsController::class, 'serviceLevelReport']);
            Route::post('getQueueWiseReport', [MnPReportsController::class, 'queueWiseReport']);
            Route::post('getCLIAbandonCalls', [MnPReportsController::class, 'getCLIAbandonCalls']);
            Route::post('getAgentKPIReport', [MnPReportsController::class, 'getAgentKPIReport']);
            Route::post('workcode-agent-wise', [InboundDispositionSummaryController::class, 'getWorkcodeAgentWise']);
            Route::post('workcode-date-wise', [InboundDispositionSummaryController::class, 'getWorkcodeDateWise']);
        });

        // Agent Routes
        Route::group(['prefix' => 'agent'], function () {
            Route::get('get-queue', [AgentController::class, 'get_queue']);
            Route::post('login', [AgentController::class, 'login_queue']);
            Route::post('logout', [AgentController::class, 'logout_queue']);
            Route::post('pause', [AgentController::class, 'pause_queue']);
            Route::post('unpause', [AgentController::class, 'unpause_queue']);
            Route::post('hold', [AgentController::class, 'hold']);
            Route::post('un-hold', [AgentController::class, 'un_hold']);
            Route::post('status', [AgentController::class, 'agent_status']);
            Route::post('workcode', [AgentController::class, 'submit_workcode']);
            Route::post('channel', [AgentController::class, 'get_channel_id']);
            Route::post('outgoing-channel', [AgentController::class, 'getOutgoingChannelId']);
            Route::post('is-ready', [AgentController::class, 'is_ready']);
            Route::post('is-login', [AgentController::class, 'is_login']);
            Route::post('queue-stats', [AgentController::class, 'get_queue_stats']);
            Route::post('agent-stats', [AgentController::class, 'get_agent_stats']);
            Route::post('agent-cdr', [AgentController::class, 'get_agent_cdr']);
            Route::post('agent-status', [AgentController::class, 'get_agent_status']);
            Route::post('save-data', [AgentController::class, 'saveData']);
            Route::post('call-history', [CallDetailRecordController::class, 'getCallHistory']);
            Route::get('/form/{queue}', [AgentController::class, 'getForms']);
            Route::post('/getTime', [AgentController::class, 'getTime']);
            Route::get('scheduleCalls', [ScheduleCallsController::class, 'index']);
            Route::post('scheduleCalls', [ScheduleCallsController::class, 'store']);
            Route::put('scheduleCalls/{scheduleCalls}', [ScheduleCallsController::class, 'update']);
            //Route::get('/notification', [NotificationController::class, 'index']);

            // dtmf input
            Route::post('get-dtmf-input', [AgentController::class, 'get_dtmf_input']);

            Route::group(['prefix' => 'conversation'], function () {
                Route::post('/create/{id}', [ConversationController::class, 'createRoom']);
                Route::get('/allUsers', [ConversationController::class, 'getAllUsers']);
                Route::get('/convUsers', [ConversationController::class, 'conversationList']);
                Route::get('/chatroom/{id}', [ConversationController::class, 'chatRoom']);
                Route::post('/send', [ConversationController::class, 'sendMessage']);
                Route::post('/chat/delete', [ConversationController::class, 'deleteConversation']);
                Route::post('delivered/{id}', \App\Http\Controllers\MessageDelieveredController::class);
                Route::get('/messages/unread-count', [ConversationController::class, 'getUnreadMessageCount']);
                Route::post('/messages/mark-as-read', [ConversationController::class, 'markAsRead']);

                Route::get('/test', [ConversationController::class, 'test']);
            });
        });

        // Custom Routes
        Route::get('/getEnumValues', [QueueController::class, 'getEnumValues']);

        // Upload route
        Route::post('/upload/audio', [AudioController::class, 'store']);
        Route::post('/upload/file', [FileController::class, 'store']);

        //advance functionality of form data feature
        //for showing list on admin panel
        Route::get('formData', [FormDataController::class, 'index']);
        //for store and update data after call
        Route::post('formData', [FormDataController::class, 'store']);
        //for get single form detail record call
        Route::get('formData/{formData}', [FormDataController::class, 'show']);
        //for store and update data after call
        Route::put('formData/{formData}', [FormDataController::class, 'update']);
        //for deletng specific record from admin
        Route::delete('formData/{formData}', [FormDataController::class, 'destroy']);
        //for showing data on agent console form
        Route::get('getFormData', [FormDataController::class, 'getData']);
        //for importing data from admin panel
        Route::post('importFormData', [FormDataController::class, 'importData']);
        // prepaidRoutes

        Route::get('/prepaid', [PrepaidController::class, 'getPrepaid']);
        Route::put('/prepaid', [PrepaidController::class, 'update']);
        Route::get('/prepaid-bil', [PrepaidController::class, 'bilSum']);
        Route::post('/prepaid-mood', [PrepaidController::class, 'switchPrepaid']);
        Route::get('/check-available-minutes', [PrepaidController::class, 'checkRemainingMinutesAndNotify']);
        // Gmail fetch
        Route::get('/fetch-emails', [GmailController::class, 'fetchEmails']);
        Route::post('/reply-emails', [GmailController::class, 'replyToMail']);
        Route::get('/getEmailSettings', [GmailController::class, 'getEmailSettings']);
        // Route::post('/updateEmailSettings/{id}', [GmailController::class, 'update']);
        Route::post('/createOrUpdateEmailSettings', [GmailController::class, 'createUpdate']);
        Route::post('/updateReplyEmailSettings', [GmailController::class, 'replayUpdate']);

        // Agentless campaign
        /*
         ****
         *****Agentless Campaign Routes
         */

        Route::prefix('agentless')->group(function () {

            Route::apiResource('/servers', CallingServerController::class);
            Route::get('/recordings', [RecordingController::class, 'index']);
            Route::post('/recording', [RecordingController::class, 'recordingStore']);
            Route::delete('/recording/{id}', [RecordingController::class, 'destroy']);
            /*
             ******Campaign
             */
            Route::get('/campaigns', [AgentlessCampaignController::class, 'getCampaigns']);
            Route::get('/campaign/{campaign}', [AgentlessCampaignController::class, 'getSingleCampaign']);
            Route::post('/campaign', [AgentlessCampaignController::class, 'createCampaign']);
            Route::post('/campaign/{campaign}', [AgentlessCampaignController::class, 'activateCampaign']);
            Route::get('/viewCampaignSummary/{campaign}', [AgentlessCampaignController::class, 'viewReportSummary']);
            Route::get('/stopCampaign/{campaign}', [AgentlessCampaignController::class, 'stopCampaign']);
            Route::get('/restartCampaign/{campaign}', [AgentlessCampaignController::class, 'restartCampaign']);
            Route::get('/campaignExport/{campaign}', [AgentlessCampaignController::class, 'campaignExport']);
            Route::delete('/campaign/{campaign}', [AgentlessCampaignController::class, 'destroy']);
        });

        // Email
        Route::get('/emails', [EmailController::class, 'index']);
        Route::post('/replies', [EmailController::class, 'store']);
    });
    // endauth

    // Dashboard Routes

    Route::group(['prefix' => 'dashboard'], function () {
        Route::get('/', [AgentDashboardController::class, 'index']);
        Route::get('/agent', [AgentDashboardController::class, 'agent_data']);
        Route::get('/queue', [AgentDashboardController::class, 'getQueues']);
        Route::get('/outbound', [OutboundDashboardController::class, 'getStats']);
        Route::get('/outbound-agent', [OutboundDashboardController::class, 'getAgentStats']);
        Route::get('/ticker', [DashboardController::class, 'ticker']);
        Route::put('/ticker/{ticker}', [DashboardController::class, 'update']);
    });

    //Campaign Monitoring

    Route::group(['prefix' => 'monitoring'], function () {
        Route::post('/campaign-agent', [CampaignController::class, 'getCampaignsMonitoring']);
        Route::post('/campaign-agent/{campaign}', [CampaignController::class, 'getCampaignMonitoring']);
    });

    // API Routes

    Route::group(['prefix' => 'click_to_call'], function () {
        Route::post('/', [CallApiController::class, 'click_to_call']);
    });


    Route::get('file/{recordingFile}', [RecordingController::class, 'getFile']); //this for agentlesscampaign file
    Route::get('/monitor-file/{recordingFile}', [RecordingController::class, 'monitorFile']); //this for  Call Detail Report file
    //FOR API INTEGRATION
    Route::resource('cIDLoopUp', \App\Http\Controllers\CIDLoopUpController::class);

    //Route::get('download/recordingFile',[\App\Http\Controllers\RecordingController::class, 'getFiles']);

    // Downloading recording files
    Route::group(['prefix' => 'download'], function () {
        Route::get('/{recordingFile}', [RecordingController::class, 'get']);
    });

    // Agent monitoring routes
    Route::group(['prefix' => 'agent-monitoring'], function () {
        Route::get('/getQueue', [\App\Http\Controllers\AgentMonitoringController::class, 'getQueuesForMonitoring']);
        Route::get('/getBreak', [\App\Http\Controllers\AgentMonitoringController::class, 'getBreaksForMonitoring']);
        Route::get("/getSystemSettings", [\App\Http\Controllers\AgentMonitoringController::class, 'getSystemSettings']);
        Route::get('/getAgentLogs/{queue}', [\App\Http\Controllers\AgentMonitoringController::class, 'getAgentLogs']);
        Route::get('/monitor/{queue}', [\App\Http\Controllers\AgentMonitoringController::class, 'getMonitoringData']);
        Route::get('/summary/{queue}', [\App\Http\Controllers\AgentMonitoringController::class, 'getSummaryData']);
        Route::get('/agent/{queue}', [\App\Http\Controllers\AgentMonitoringController::class, 'getAgentData']);
        Route::get('/logoutAgent/{queue}', [\App\Http\Controllers\AgentMonitoringController::class, 'logoutAgentFromQueue']);
        Route::get('/breakAgent/{queue}', [\App\Http\Controllers\AgentMonitoringController::class, 'sendAgentOnBreak']);
        Route::get('/lastLogin/{queue}', [\App\Http\Controllers\AgentMonitoringController::class, 'getLastLoginOfAgent']);
        Route::get('/getMonitoringDashBoardData', [\App\Http\Controllers\AgentMonitoringController::class, 'monitoringDashBoardData']);
        Route::get('/getPerQueueData', [\App\Http\Controllers\AgentMonitoringController::class, 'getAgentDataQwise']);
        Route::get('/getLoginMember', [\App\Http\Controllers\AgentMonitoringController::class, 'getSummaryDataQwise']);
    });
    //Bitrix integartion

    //password reset by admin

    Route::apiResource('dtmf-settings', DTMFSettingController::class);
});


Route::get('/agentless-command', [AgentlessTestController::class, 'command']);
