<?php

namespace App\Console;

use App\Console\Commands\ActivateCampaigns;
use App\Console\Commands\AgentlessCampaignCommand;
use App\Console\Commands\AgentWiseReportDailyCommand;
use App\Console\Commands\AgentWiseReportMonthlyCommand;
use App\Console\Commands\CoreReload;
use App\Console\Commands\LogoutAllUnavailableAgents;
use App\Console\Commands\ResetQueueStats;
use App\Console\Commands\RestartIntegrationWorker;
use App\Console\Commands\RestartLaravelCommand;
use App\Console\Commands\StoreDtmf;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        StoreDtmf::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // $schedule->command('inspire')->hourly();
        // Check if campaign has started
        $schedule->command(LogoutAllUnavailableAgents::class)->everyMinute();
        // $schedule->command(ActivateCampaigns::class)->everyMinute()->withoutOverlapping()->appendOutputTo("storage/logs/schedule.log");
        $schedule->command(ResetQueueStats::class)->dailyAt('00:00')->appendOutputTo('storage/logs/reset-queue-stats.log');
        $schedule->command(RestartIntegrationWorker::class)->hourly()->appendOutputTo('storage/logs/restart-integration-worker.log');
        $schedule->command(RestartLaravelCommand::class)->hourly()->appendOutputTo('storage/logs/restart-laravel-commad.log');
        $schedule->command(AgentWiseReportDailyCommand::class)->dailyAt('00:10')->runInBackground()->appendOutputTo('storage/logs/reports-output.log');
        $schedule->command(AgentWiseReportMonthlyCommand::class)->monthlyOn(01, '00:30')->runInBackground()->appendOutputTo('storage/logs/reports-output.log');
        $schedule->command('backup:run --only-db')->dailyAt('01:00');
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
