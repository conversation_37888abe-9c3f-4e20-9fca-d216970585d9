<?php

namespace App\Http\Controllers;

use App\Models\FormData;
use App\Models\Form;
use Illuminate\Http\Request;
use App\Models\FormFunction;
use App\Models\FormField;
use Maatwebsite\Excel\Facades\Excel;
use DB;
use Carbon\Carbon;
use App\Models\User;
use App\Models\Cdr;
use App\Exports\MergeCdrFormDataExport;

class AspireCustomFormDataController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        try {

            $records = $request->record;
            $form_id = $request->form_id;
            $record = DB::table('form_data')->where('form_id', $request->form_id)
                ->join('cdr', 'form_data.call_id', '=', 'cdr.uniqueid')
                ->leftJoin("users", function ($join) {
                    $join->on("cdr.channel", "LIKE", DB::raw("CONCAT('%PJSIP/', users.auth_username, '%')"))
                        ->orOn("cdr.dstchannel", "LIKE", DB::raw("CONCAT('%PJSIP/', users.auth_username, '%')"));
                })
                ->select('cdr.recordingfile', 'users.name', 'cdr.disposition', 'cdr.duration', 'cdr.start', 'cdr.end', 'cdr.accountcode', 'cdr.src', 'cdr.dst', 'form_data.phone_no', 'form_data.data', 'form_data.form_id', 'form_data.call_id');

            if ($request->has('start') && $request->has('end')) {
                $start = Carbon::parse($request->start)->timezone('Asia/Karachi')->toDateTimeString();
                $end = Carbon::parse($request->end)->timezone("Asia/Karachi")->toDateTimeString();
                $record->where('cdr.start', '>=', $start)->where('cdr.start', '<=', $end);
            }

            if ($request->has('destination')) {
                $record->where('cdr.dst', 'like', "%{$request->destination}%");
            }

            if ($request->has('source')) {
                $record->where('cdr.src', 'like', "%{$request->source}%");
            }
            if ($request->has('callStatus')) {
                $record->where('cdr.disposition', $request->callStatus);
            }
            //dd($record->get()->toArray());
           
            $headings = DB::table('form_fields')->where('form_id', $request->form_id)->get();
            return response()->json(['record' => $record->paginate($records ?? 15), 'headings' => $headings]);

        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }

    }

    public function getFormfilterModelData(Request $request)
    {
        $formId = Form::query()->select('id', 'name')->get();
        $callStatus = Cdr::query()->select('disposition')->distinct()->get();
        $agents = User::query()->whereNotNull('auth_username')->get(['username', 'auth_username']);
        return response()->json(['formIds' => $formId, 'callStatus' => $callStatus, 'agents' => $agents]);
    }

    public function exportCustomFormData(Request $request) 
    {
        $form_id = $request->form_id;
        // dd($request->all());
            $record = FormData::where('form_id', $request->form_id)
                ->join('cdr', 'form_data.call_id', '=', 'cdr.uniqueid')
                ->leftJoin("users", function ($join) {
                    $join->on("cdr.channel", "LIKE", DB::raw("CONCAT('%PJSIP/', users.auth_username, '%')"))
                        ->orOn("cdr.dstchannel", "LIKE", DB::raw("CONCAT('%PJSIP/', users.auth_username, '%')"));
                })
                ->select('cdr.recordingfile', 'users.name', 'cdr.disposition', 'cdr.duration', 'cdr.start', 'cdr.end', 'cdr.accountcode', 'cdr.src', 'cdr.dst', 'form_data.phone_no', 'form_data.data', 'form_data.form_id', 'form_data.call_id');

            //dd($request->time);
            if ($request->has('start') && $request->has('end')) {
                $start = Carbon::parse($request->start)->timezone('Asia/Karachi')->toDateTimeString();
                $end = Carbon::parse($request->end)->timezone("Asia/Karachi")->toDateTimeString();
                $record->where('cdr.start', '>=', $start)->where('cdr.start', '<=', $end);
            }

            if ($request->has('destination') && $request->destination != null) {
                $record->where('cdr.dst', 'like', "%{$request->destination}%");
            }

            if ($request->has('source') && $request->source != null) {
                $record->where('cdr.src', 'like', "%{$request->source}%");
            }
            if ($request->has('callStatus') && $request->callStatus != null) {
                $record->where('cdr.disposition', $request->callStatus);
            }

            return Excel::download(new MergeCdrFormDataExport($record->get()->toArray(), $form_id) ,'MergeCdrFormDataExport.csv');

    }


}