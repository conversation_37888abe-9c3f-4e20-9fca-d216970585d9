<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use App\Models\Cdr;
use App\Models\Queue;
use App\Models\QueueLog;
use App\Models\User;
use DateTime;
use Illuminate\Support\Facades\DB;
use Carbon\CarbonPeriod;
use Carbon\CarbonInterval;
use Symfony\Component\Console\Command\Command as CommandAlias;
use App\Models\MnpReports;

class AgentWiseReportDailyCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'agent:wise-daily-report';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This report will fetch agent wise report daily';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */

    private function getUsers($start, $end, $queue, $fetchFromQueueLog = true)
    {
        if ($fetchFromQueueLog) {
            // return DB::select("select DISTINCT Agent as name, u.auth_username from queue_log left join users u on u.name = Agent where DATE(time) >= '$start' and DATE(time) <= '$end' and queuename = '$queue' and Agent <> 'NONE' group by Agent order by time desc");

            $users =  QueueLog::select('Agent as name', 'users.auth_username', 'users.username')
                ->leftJoin('users', 'users.name', '=', 'queue_log.Agent')
                ->where('queue_log.Agent', '<>', 'NONE')
                ->groupBy('queue_log.Agent', 'users.auth_username')
                ->orderByDesc('queue_log.time')->distinct()->get();
            return $users;
        } else {
            return User::query()->join('queue_user', 'users.id', 'queue_user.user_id')->select(['name', 'auth_username'])->whereIn('type', ['Blended', 'Inbound'])->where('queue_user.queue_name', $queue)->get();
        }
    }
    public function handle()
    {
        // $start = '2025-02-11';
        // $end = '2025-02-11';
        $start = Carbon::yesterday()->toDateString();
        $end = Carbon::yesterday()->toDateString();
        $queues = DB::select("select DISTINCT queuename from queue_log where DATE(time) >= '$start' and DATE(time) <= '$end' and queuename <> 'NONE' ");
        foreach ($queues as $queue) {
            $now = now();
            $this->info("[{$now->toDateTimeString()}] INFO: Process started for Queue {$queue->queuename} Agent Wise(KPI) Daily.");

            $period = CarbonPeriod::create($start, $end);
            $users = $this->getUsers($start, $end, $queue->queuename);
            $threshold = Queue::where('name', $queue->queuename)->first();
            $total = [];
            $totalData = [];

            $TotalLoginTime = 0;
            $TotalBreakTime = 0;
            $TotalHoldTime = 0;
            $TotalTalkTime = 0;
            $TotalIncomingCalls = 0;
            $TotalAwcTime = 0;
            $TotalAnsweredCalls = 0;
            $TotalAbandonedCalls = 0;
            $TotalThresholdCalls = 0;
            $TotalAfterThresholdCalls = 0;
            $TotalThresholdAbandonCalls = 0;
            $TotalAfterThresholdAbandonCalls = 0;
            $TotalOutboundCalls = 0;
            $TotalAHT = 0;
            $TotalAgentProductivity = 0;
            $TotalAHTOutput = 0;
            foreach ($users as $user) {
                $loginTime = 0;
                $breakTime = 0;
                $holdTime = 0;
                $talkTime = 0;
                $incomingCalls = 0;
                $answeredCalls = 0;
                $abandonedCalls = 0;
                $thresholdCalls = 0;
                $afterthresholdCalls = 0;
                $thresholdabandonCalls = 0;
                $thresholdafterabandonCalls = 0;
                $outboundCalls = 0;
                $AHT = 0;
                $agentProductivity = 0;
                $AHTOutput = 0;
                $awcTime = 0;

                foreach ($period as $dt) {
                    //for logged-in time calculation
                    $data = DB::select("SELECT * FROM queue_log WHERE EVENT IN ('ADDMEMBER', 'REMOVEMEMBER') AND Agent = '$user->name' AND DATE(time) = '$dt' ORDER BY TIME ASC");
                    for ($i = 0; $i < count($data); $i++) {
                        if ($data[$i]->Event === 'ADDMEMBER') {
                            // Search for remove member
                            if ($i !== count($data) - 1 && $data[$i + 1]->Event === 'REMOVEMEMBER') {
                                // Take difference
                                $loginTime += Carbon::parse($data[$i + 1]->time)->diffInSeconds($data[$i]->time);
                            } else if ($dt->format('Y-m-d') === Carbon::now()->format('Y-m-d')) {
                                $removeMember = Carbon::now()->format('Y-m-d H:i:s');
                                $loginTime += Carbon::parse($removeMember)->diffInSeconds($data[$i]->time);
                            } else {
                                $removeMember = $dt->copy()->endOfDay()->format('Y-m-d H:i:s');
                                $loginTime += Carbon::parse($removeMember)->diffInSeconds($data[$i]->time);
                            }
                        }
                    }

                    $breakTypes = DB::table('queue_log')->select('data1')->distinct()->where('Event', 'PAUSE')->where('data1', '!=', '')->pluck('data1');
                    $baseQueueLogQuery = QueueLog::query()->whereDate('time', $dt)->whereIn('Event', ['PAUSE', 'UNPAUSE'])->whereNotIn('data1', ['Not Submit Workcode Yet']);
                    $baseQueueLogQuery->whereIn('data1', $breakTypes);
                    $userQueueLog = $baseQueueLogQuery->where('Agent', $user->name)->get();

                    $breakDurations = [
                        'Bio Break' => 0,
                        'Working on CXM' => 0,
                        'Namaz with lunch break' => 0,
                        'Counselling/Training' => 0,
                        'Support on HFC Desk' => 0,
                        'After login set not ready' => 0,
                    ];
                    $breakTime = 0;

                    $pauseStartTimes = [];
                    foreach ($userQueueLog as $logEntry) {
                        $breakType = $logEntry->data1;
                        $eventTime = \Carbon\Carbon::parse($logEntry->time);

                        if ($logEntry->Event == 'PAUSE') {
                            // Store the pause start time for the break type
                            $pauseStartTimes[$breakType] = $eventTime;

                            // if (!isset($pauseStartTimes[$breakType])) {
                            //     $pauseStartTimes[$breakType] = $eventTime;
                            // }

                        } elseif ($logEntry->Event == 'UNPAUSE' && isset($pauseStartTimes[$breakType])) {
                            // Calculate the time difference when unpausing
                            $pauseStartTime = $pauseStartTimes[$breakType];
                            $duration = $eventTime->diffInSeconds($pauseStartTime);

                            // Add the duration to the appropriate break type
                            $breakDurations[$breakType] += $duration;
                            $breakTime += $duration;

                            // Reset the pause start time
                            unset($pauseStartTimes[$breakType]);
                        }
                    }

                    // Convert seconds into H:i:s format
                    foreach ($breakDurations as $key => $seconds) {
                        $breakSeconds = round($seconds);
                        $breakDurations[$key] = sprintf('%02d:%02d:%02d', ($breakSeconds / 3600), ($breakSeconds / 60 % 60), $breakSeconds % 60);
                    }

                    /*
                    $breakEvents = [
                        'Bio Break',
                        'Working on CXM',
                        'Namaz with lunch break',
                        'Counselling/Training',
                        'Support on HFC Desk',
                        'After login set not ready',
                    ];
                    //for users total break
                    // $breakData = DB::select("SELECT * FROM queue_log WHERE EVENT IN ('PAUSE', 'UNPAUSE') AND data1 IN() AND Agent = '$user->name' AND DATE(time) = '$dt' ORDER BY TIME ASC");

                    $breakData = QueueLog::whereIn('event', ['PAUSE', 'UNPAUSE'])
                        ->where('agent', $user->name)
                        ->whereDate('time', $dt)

                        ->where('data1', '!=', 'Not Submit Workcode Yet')// Add your conditions inside this array
                        ->orderBy('time', 'asc')
                        ->get();

                    // Filter the events to maintain the correct PAUSE and UNPAUSE sequence
                    $filteredData = [];
                    for ($x = 0; $x < count($breakData); $x++) {
                        if ($x == 0 || $breakData[$x]->Event !== $breakData[$x - 1]->Event) {
                            $filteredData[] = $breakData[$x];
                        }
                    }

                    // Calculate the break time
                    $breakTime = 0;
                    for ($x = 0; $x < count($filteredData); $x++) {
                        if ($filteredData[$x]->Event === 'PAUSE') {
                            // Search for UNPAUSE
                            if ($x !== count($filteredData) - 1 && $filteredData[$x + 1]->Event === 'UNPAUSE') {
                                // Take difference
                                $breakTime += Carbon::parse($filteredData[$x + 1]->time)->diffInSeconds($filteredData[$x]->time);
                            } else if ($dt->format('Y-m-d') === Carbon::now()->format('Y-m-d')) {
                                $unpause = Carbon::now()->format('Y-m-d H:i:s');
                                $breakTime += Carbon::parse($unpause)->diffInSeconds($filteredData[$x]->time);
                            } else {
                                $unpause = $dt->copy()->endOfDay()->format('Y-m-d H:i:s');
                                $breakTime += Carbon::parse($unpause)->diffInSeconds($filteredData[$x]->time);
                            }
                        }
                    }

                    */
                    //for hold time
                    $holdData = DB::select("SELECT * FROM queue_log WHERE EVENT IN('HOLD','UNHOLD','COMPLETECALLER' ,'COMPLETEAGENT', 'BLINDTRANSFER') AND Agent = '$user->name' AND callid != 'NONE' AND DATE(time) = '$dt' ORDER BY TIME ASC");
                    for ($y = 0; $y < count($holdData); $y++) {
                        if ($holdData[$y]->Event === 'HOLD') {
                            if ($y !== count($holdData) - 1 && ($holdData[$y + 1]->Event === 'UNHOLD' || $holdData[$y + 1]->Event === 'COMPLETECALLER' || $holdData[$y + 1]->Event === 'COMPLETEAGENT' || $holdData[$y + 1]->Event === 'BLINDTRANSFER') && $holdData[$y]->callid === $holdData[$y + 1]->callid) {
                                // Take difference
                                $holdTime += Carbon::parse($holdData[$y + 1]->time)->diffInSeconds($holdData[$y]->time);
                            }
                        }
                    }
                    // $awcData = DB::select("SELECT * FROM queue_log WHERE EVENT IN('COMPLETECALLER' ,'COMPLETEAGENT', 'WORKCODE') AND Agent = '$user->name' AND callid != 'NONE' AND DATE(time) = '$dt' ORDER BY TIME ASC");

                    // // Filter the events to ensure the sequence starts with COMPLETECALLER or COMPLETEAGENT, followed by WORKCODE
                    // $filteredDataAWC = [];


                    // for ($y = 0; $y < count($awcData); $y++) {
                    //     if ($awcData[$y]->Event === 'COMPLETECALLER' || $awcData[$y]->Event === 'COMPLETEAGENT') {
                    //         // Check if the next event is WORKCODE and has the same callid
                    //         if ($y !== count($awcData) - 1 && $awcData[$y + 1]->Event === 'WORKCODE' && $awcData[$y]->callid === $awcData[$y + 1]->callid) {
                    //             $filteredDataAWC[] = $awcData[$y];
                    //             $filteredDataAWC[] = $awcData[$y + 1];
                    //             $y++; // Skip the next event since it's already processed
                    //         }
                    //     }
                    // }

                    // Calculate the AWC time
                    // $awcTime = 0;
                    // for ($y = 0; $y < count($filteredDataAWC); $y++) {
                    //     if ($filteredDataAWC[$y]->Event === 'COMPLETECALLER' || $filteredDataAWC[$y]->Event === 'COMPLETEAGENT') {
                    //         // The next event is guaranteed to be WORKCODE
                    //         $awcTime += Carbon::parse($filteredDataAWC[$y + 1]->time)->diffInSeconds($filteredDataAWC[$y]->time);
                    //         $y++; // Skip the WORKCODE event since it's already processed
                    //     }
                    // }

                    // Calculate the AWC time
                    $records = QueueLog::query()->where('Agent', $user->name)->whereDate('time', $dt)->whereIn('Event', ['ADDMEMBER', 'REMOVEMEMBER', 'PAUSE', 'UNPAUSE'])->get();
                    $pauseStartTimes = [];

                    foreach ($records as $logEntry) {
                        $breakType = $logEntry->data1;
                        $agentName = $logEntry->Agent;
                        $eventTime = Carbon::parse($logEntry->time);
                        $timeOut = null;

                        if ($breakType == 'Not Submit Workcode Yet') {
                            if ($logEntry->Event == 'PAUSE') {
                                // Store the pause start time for the break type and agent
                                $pauseStartTimes[$agentName][$breakType] = $eventTime;
                            }
                            // Check if this is an UNPAUSE event and the corresponding PAUSE event exists
                            elseif ($logEntry->Event == 'UNPAUSE' && isset($pauseStartTimes[$agentName][$breakType])) {
                                // Calculate the time difference when unpausing
                                $timeOut = Carbon::parse($logEntry->time);
                                $pauseStartTime = $pauseStartTimes[$agentName][$breakType];
                                $awcTime += $timeOut->diffInSeconds($pauseStartTime);

                                // Reset the pause start time after processing
                                unset($pauseStartTimes[$agentName][$breakType]);
                            }
                        }
                    }

                    $thresholdValue = $threshold->servicelevel;
                    $query = "
                    SELECT
                        COUNT(CASE WHEN Event IN ('CONNECT', 'RINGNOANSWER') THEN 1 END) as incomingCalls,
                        COUNT(CASE WHEN Event = 'CONNECT' THEN 1 END) as answeredCalls,
                        COUNT(CASE WHEN Event = 'RINGNOANSWER' THEN 1 END) as abandonedCalls,
                        COUNT(CASE WHEN Event = 'CONNECT' AND data1 <= :threshold THEN 1 END) as thresholdCalls,
                        COUNT(CASE WHEN Event = 'CONNECT' AND data1 > $thresholdValue THEN 1 END) as answerAfterThreshold,
                        COUNT(CASE WHEN Event = 'RINGNOANSWER' AND data1 <= $thresholdValue THEN 1  END) as abandonInThreshold,
                        COUNT(CASE WHEN Event = 'RINGNOANSWER' AND data1 > $thresholdValue THEN 1 END) as abandonAfterThreshold,
                        SUM(
                            CASE
                                WHEN Event IN ('COMPLETECALLER', 'COMPLETEAGENT') THEN data2
                                WHEN Event = 'BLINDTRANSFER' AND data2 = 'asyncagi_rating' THEN data4
                                ELSE 0

                            END
                        ) as talkTime

                    FROM
                        queue_log
                    WHERE
                        Agent = :user
                    AND
                        DATE(time) = :date
                    ";

                    $data = DB::select($query, ['user' => $user->name, 'date' => $dt, 'threshold' => $thresholdValue]);
                    $incomingCalls += $data[0]->incomingCalls;
                    $answeredCalls += $data[0]->answeredCalls;
                    $abandonedCalls += $data[0]->abandonedCalls;
                    $thresholdCalls += $data[0]->thresholdCalls;
                    $afterthresholdCalls += $data[0]->answerAfterThreshold;
                    $thresholdabandonCalls += $data[0]->abandonInThreshold;
                    $thresholdafterabandonCalls += $data[0]->abandonAfterThreshold;


                    $talkTime += $data[0]->talkTime;
                    //for outbound call
                    if (isset($user->auth_username)) {
                        $outboundCalls += Cdr::where('accountcode', 'Outbound')->where('channel', 'LIKE', DB::raw("CONCAT('%PJSIP/', $user->auth_username, '%')"))->whereDate('start', $dt)->count();
                    }
                }

                if ($answeredCalls != 0) {
                    $AHT += ($talkTime + $holdTime + $threshold->wrapuptime) / $answeredCalls;
                    $AHTSeconds = round($AHT);
                    $AHTOutput = sprintf('%02d:%02d:%02d', ($AHTSeconds / 3600), ($AHTSeconds / 60 % 60), $AHTSeconds % 60);
                }
                //for agent productivity
                if ($loginTime != 0) {
                    $agentProductivity += round(($talkTime / $loginTime) * 100);
                }


                $loginSeconds = round($loginTime);
                $loginOutput = sprintf('%02d:%02d:%02d', ($loginSeconds / 3600), ($loginSeconds / 60 % 60), $loginSeconds % 60);

                $breakSeconds = round($breakTime);
                $breakOutput = sprintf('%02d:%02d:%02d', ($breakSeconds / 3600), ($breakSeconds / 60 % 60), $breakSeconds % 60);

                $talkSeconds = round($talkTime);
                $talkOutput = sprintf('%02d:%02d:%02d', ($talkSeconds / 3600), ($talkSeconds / 60 % 60), $talkSeconds % 60);

                $holdSeconds = round($holdTime);
                $holdOutput = sprintf('%02d:%02d:%02d', ($holdSeconds / 3600), ($holdSeconds / 60 % 60), $holdSeconds % 60);

                $awcSeconds = round($awcTime);
                $awcOutput = sprintf('%02d:%02d:%02d', ($awcSeconds / 3600), ($awcSeconds / 60 % 60), $awcSeconds % 60);

                $total[] = [
                    'Agent' => $user->name,
                    'loginTime' => $loginOutput,
                    'breakTime' => $breakOutput,
                    'incomingCalls' => $incomingCalls,
                    'answeredCalls' => $answeredCalls,
                    'abandonedCalls' => $abandonedCalls,
                    'thresholdCalls' => $thresholdCalls,
                    'afterThresholdCalls' => $afterthresholdCalls,
                    'thresholdabandonCalls' => $thresholdabandonCalls,
                    'thresholdafterabandonCalls' => $thresholdafterabandonCalls,
                    'outboundCalls' => $outboundCalls,
                    'talkTime' => $talkOutput,
                    'holdTime' => $holdOutput,
                    'awcOutput' => $awcOutput,
                    'AHT' => ($AHTOutput == 0) ? '00:00:00' : $AHTOutput,
                    'agentProductivity' => $agentProductivity . '%'
                ];

                //assigning total values of a user in grand total variables
                $TotalLoginTime += $loginTime;
                $TotalBreakTime += $breakTime;
                $TotalIncomingCalls += $incomingCalls;
                $TotalAnsweredCalls += $answeredCalls;
                $TotalAbandonedCalls += $abandonedCalls;
                $TotalThresholdCalls += $thresholdCalls;
                $TotalAfterThresholdCalls += $afterthresholdCalls;
                $TotalThresholdAbandonCalls += $thresholdabandonCalls;
                $TotalAfterThresholdAbandonCalls += $thresholdafterabandonCalls;
                $TotalOutboundCalls += $outboundCalls;
                $TotalTalkTime += $talkTime;
                $TotalHoldTime += $holdTime;
                $TotalAwcTime += $awcTime;
            }

            //calculation for grandtotal

            if ($TotalAnsweredCalls != 0) {
                $TotalAHT += ($TotalTalkTime + $TotalHoldTime + $threshold->wrapuptime) / $TotalAnsweredCalls;
                $TotalAHTSeconds = round($TotalAHT);
                $TotalAHTOutput = sprintf('%02d:%02d:%02d', ($TotalAHTSeconds / 3600), ($TotalAHTSeconds / 60 % 60), $TotalAHTSeconds % 60);
            }
            //for agent productivity
            if ($TotalLoginTime != 0) {
                $TotalAgentProductivity += round(($TotalTalkTime / $TotalLoginTime) * 100);
            }

            $TotalLoginSeconds = round($TotalLoginTime);
            $TotalLoginOutput = sprintf('%02d:%02d:%02d', ($TotalLoginSeconds / 3600), ($TotalLoginSeconds / 60 % 60), $TotalLoginSeconds % 60);

            $TotalAwcSeconds = round($TotalAwcTime);
            $TotalAwcOutput = sprintf('%02d:%02d:%02d', ($TotalAwcSeconds / 3600), ($TotalAwcSeconds / 60 % 60), $TotalAwcSeconds % 60);

            $TotalBreakSeconds = round($TotalBreakTime);
            $TotalBreakOutput = sprintf('%02d:%02d:%02d', ($TotalBreakSeconds / 3600), ($TotalBreakSeconds / 60 % 60), $TotalBreakSeconds % 60);

            $TotalTalkSeconds = round($TotalTalkTime);
            $TotalTalkOutput = sprintf('%02d:%02d:%02d', ($TotalTalkSeconds / 3600), ($TotalTalkSeconds / 60 % 60), $TotalTalkSeconds % 60);

            $TotalHoldSeconds = round($TotalHoldTime);
            $TotalHoldOutput = sprintf('%02d:%02d:%02d', ($TotalHoldSeconds / 3600), ($TotalHoldSeconds / 60 % 60), $TotalHoldSeconds % 60);



            $totalData[] = [
                'Agent' => 'Total',
                'loginTime' => $TotalLoginOutput,
                'breakTime' => $TotalBreakOutput,
                'incomingCalls' => $TotalIncomingCalls,
                'answeredCalls' => $TotalAnsweredCalls,
                'abandonedCalls' => $TotalAbandonedCalls,
                'thresholdCalls' => $TotalThresholdCalls,
                'afterThresholdCalls' => $TotalAfterThresholdCalls,
                'thresholdabandonCalls' => $TotalThresholdAbandonCalls,
                'thresholdafterabandonCalls' => $TotalAfterThresholdAbandonCalls,
                'outboundCalls' => $TotalOutboundCalls,
                'talkTime' => $TotalTalkOutput,
                'holdTime' => $TotalHoldOutput,
                'awcOutput' => $TotalAwcOutput,
                'AHT' => ($TotalAHTOutput == 0) ? '00:00:00' : $TotalAHTOutput,
                'agentProductivity' => $TotalAgentProductivity . '%',
            ];
            // //dd($data);
            $grandTotal = array_merge($total, $totalData);
            //$this->info(json_encode($grandTotal));
            $record = new MnpReports;
            $record->report_name = 'Agent Wise Report';
            $record->report_type = 'Daily';
            $record->report_date = $start;
            $record->queue = $queue->queuename;
            $record->data = json_encode($grandTotal);
            $record->save();
            $now = now();
            $this->info("[{$now->toDateTimeString()}] INFO: Process Completed for Agent Wise(KPI) Daily.");
        }

        return CommandAlias::SUCCESS;
    }
}
