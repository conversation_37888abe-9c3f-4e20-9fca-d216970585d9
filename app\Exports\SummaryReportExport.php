<?php

namespace App\Exports;

use App\Models\Queue;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class SummaryReportExport implements FromQuery,WithHeadings,WithMapping
{
    /**
    * @return \Illuminate\Support\Collection
    */
    protected $from_date;
    protected $to_date;

    function __construct($from_date,$to_date) {
        $this->from_date = $from_date;
        $this->to_date = $to_date;
    }
    public function query() {
        $threshold = Queue::first('servicelevel');

        $reports   = DB::table('queue_log')->whereBetween(DB::raw('DATE(time)'), [$this->from_date, $this->to_date])
            ->groupBy(DB::raw("DATE(time)"))
            ->orderBy(DB::raw("DATE(time)"))
            ->select(DB::raw("
                        Date(time) as date,
                        SUM(CASE WHEN (Event = 'ENTERQUEUE') THEN 1 ELSE 0 END) AS totalCalls,
                        SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END) AS answerdCalls,
                        SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END) AS abandonCalls,
                        SUBSTRING(SEC_TO_TIME(SUM(CASE WHEN (Event = 'COMPLETECALLER' OR Event ='COMPLETEAGENT') THEN data2 ELSE 0 END)/SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)),1,8) AS averageTalkTime,
                        (SUM(CASE WHEN (Event = 'CONNECT' AND data1 <= $threshold->servicelevel) THEN 1 ELSE 0 END)+SUM(CASE WHEN (Event = 'ABANDON' AND data3 <= $threshold->servicelevel) THEN 1 ELSE 0 END))/(SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)+SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END))*100 AS SLA
                    ")
            );

            return $reports;
    }
     
    public function map($reports): array
    {
         if($reports->averageTalkTime ==NULL)
            $reports->averageTalkTime = '00:00:00';
         if($reports->SLA ==NULL)
            $reports->SLA = '0';
         else
         $reports->SLA = round($reports->SLA,2);
        return[
            $reports->date,
            $reports->totalCalls,
            $reports->answerdCalls,
            $reports->abandonCalls,
            $reports->averageTalkTime,
            $reports->SLA
        ];
    }

    public function headings(): array
    { 
        return [
            'Date',
            'Total Calls',
            'Answered Calls',
            'Ababdon Calls',
            'Average Talk Time',
            'SLA'
        ];
    }
}
