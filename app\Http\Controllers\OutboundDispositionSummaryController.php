<?php

namespace App\Http\Controllers;

use App\Models\Cdr;
use App\Models\QueueLog;
use App\Models\SystemSetting;
use App\Models\WorkCode;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class OutboundDispositionSummaryController extends Controller
{
    public function getOutboundDispositionSummary(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $outboundTrunkString = SystemSetting::GetSetting('outbound_string') ?? 'TCL-endpoint';

            $cdr = Cdr::query();
            $cdr->where('dstchannel', 'like', "%{$outboundTrunkString}%");
            
            if ($request->has('date') && is_array($request->date)) {
                $start = Carbon::parse($request->date[0])->timezone('Asia/Karachi');
                $end = Carbon::parse($request->date[1])->timezone("Asia/Karachi");
                $cdr->whereBetween("start", [$start->toDateTime(), $end->toDateTime()]);
            }
            if ($request->has('queue')) {
                $cdr->where('queue_log.queuename', 'like', "%{$request->queue}%");
            }
            
            $cdr->join('queue_log', 'uniqueid', '=', 'queue_log.callid')
                ->join('work_codes', 'id', '=', 'queue_log.data1')
                ->select('id', 'name as call_status', DB::raw('count(*) as count'))
                ->orderBy('start', 'desc')->groupBy('name');
            return response()->json($cdr->get());

        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }
}
