<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use App\Models\SystemSetting;
use PAGI\Exception\ChannelDownException;
use PAMI\AsyncAgi\AsyncClientImpl;
use PAMI\Client\Impl\ClientImpl;
use PAMI\Message\Event\AsyncAGIStartEvent;
use PAMI\Message\Event\EventMessage;
use PAMI\Message\Action\AGIAction;
use PAMI\Message\Action\HangupAction;
use Illuminate\Support\Facades\DB;
use App\Models\IvrMenuSetting;
use PAMI\Message\Action\GotoAction;
use PAMI\Message\Action\OriginateAction;
use PAMI\Message\Action\RedirectAction;

class IvrMenu extends Command
{
    protected $signature = 'ivrmenu:start';
    protected $description = 'User customizable ivr menu';

    public function __construct()
    {
        parent::__construct();
    }

    protected function getAMIOptions(): array
    {
        return [
            'host' => SystemSetting::GetSetting('server_address'),
            'scheme' => 'tcp://',
            'port' => SystemSetting::GetSetting('manager_port'),
            'username' => SystemSetting::GetSetting('username'),
            'secret' => SystemSetting::GetSetting('secret'),
            'connect_timeout' => SystemSetting::GetSetting('connection_timeout'),
            'read_timeout' => SystemSetting::GetSetting('read_timeout')
        ];
    }

    public function handle()
    {
        $this->info("IVR MENU RUNNING");
        $client = new ClientImpl($this->getAMIOptions());

        $client->registerEventListener(function (EventMessage $event) use ($client) {
            if (!$event instanceof AsyncAGIStartEvent) {
                return;
            }

            $env = $event->getKey('env');
            $this->info("ARG : $env");
            preg_match('/agi_arg_1:\s*(\S+)/', $env, $matches);
            $scriptArg = $matches[1] ?? null;
            $this->info("ARG_VALUE : $scriptArg");
            if ($scriptArg !== 'IvrMenu') {
                return;
            }
            
            $channel = $event->getChannel();
            $extension = $event->getExten();

            $agiClient = new AsyncClientImpl([
                'pamiClient' => $client,
                'asyncAgiEvent' => $event
            ]);

            $this->processIVR($agiClient, $channel, $extension);

        }, function ($event) {
            return $event instanceof AsyncAGIStartEvent;
        });

        $client->open();

        while (true) {
            $client->process();
        }
    }

    protected function processIVR($agiClient, $channel, $extension)
    {
        try {
            $this->info("IVR MENU Processing");
            $agiClient->answer();

            $this->info("Timing Res : {$this->checkTimings()}");

            if($this->checkTimings() == 0) {
                $this->info("Off-Timing");
                $menuExit = $this->fetchMenu(null, $off_working = 1);

                if (!$menuExit) {
                    $this->info("No off-working menu found, running normal flow.");
                    $agiClient->asyncBreak(); // Exit AGI script and return to dialplan
                    return;
                }

                $agiClient->streamFile($menuExit->media);
                $this->info("Running : {$menuExit->media}");
                $agiClient->exec('hangup');
                return false;
            }
            else if($this->checkTimings() == 3) {
                $this->info("No ivr settings found, running normal flow.");
                $agiClient->asyncBreak(); // Exit AGI script and return to dialplan
                return;
            }

            // Process priority 1 menu first
            $priorityMenu = $this->fetchMenuByPriority(1);

            if (!$priorityMenu) {
                $this->info("No priority menu found, running normal flow.");
                $agiClient->asyncBreak(); // Exit AGI script and return to dialplan
                return;
            }

            $priorityMenuOptions = $this->fetchMenuOptions($priorityMenu->id);
            $ivrSettings = IvrMenuSetting::query()->first();

            if ($priorityMenuOptions->isEmpty()) {
                $agiClient->streamFile($priorityMenu->media);
                $this->info("Priority menu has no options. Redirecting to $ivrSettings->queue context.");
                $client = new ClientImpl($this->getAMIOptions());
                $redirectAction = new RedirectAction($channel, $extension, $ivrSettings->queue, 1);
                $client->open();
                $client->send($redirectAction);
                $client->close();
                return;
            }

            $menuId = $priorityMenu->id ?? null; // Start with the root menu

            do {
                $menu = $this->fetchMenu($menuId, null);
                if (!$menu) {
                    $agiClient->streamFile('invalid');
                    break;
                }

                // Play menu prompt
                //$digit = $agiClient->getData($menu->media, 5000, 1)->getDigits();
                //$this->info("User Input {$digit}");

                // Check if the menu has options
                $menuOptions = $this->fetchMenuOptions($menuId);

                $hasOptionNumber = false;
                $hasQueue = false;
                $queueName = "";

                if ($menuOptions->isEmpty()) {
                    $this->info("no-option");
                    $agiClient->streamFile($menu->media);
                    //$agiClient->streamFile('ivr/exit');
                    break;
                }
                else {
                     // Play menu prompt
                     //$digit = $agiClient->getData($menu->media, 5000, 1)->getDigits();

                    foreach ($menuOptions as $menuOption) {
                        if (!is_null($menuOption->option_number)) {
                            $hasOptionNumber = true;
                        }
                        if (!is_null($menuOption->queue)) {
                            $hasQueue = true;
                            $queueName = $menuOption->queue;
                        }
                    }

                    if ($hasOptionNumber) {
                        $digit = $agiClient->getData($menu->media, 5000, 1)->getDigits();
                    }
                    elseif ($hasQueue) {
                        $agiClient->streamFile($menu->media);
                        $this->info("Priority menu has no options. Redirecting to $queueName context.");
                        $client = new ClientImpl($this->getAMIOptions());
                        $redirectAction = new RedirectAction($channel, $extension, $queueName, 1);
                        $client->open();
                        $client->send($redirectAction);
                        $client->close();
                        return;
                    }
                    else {
                        $this->info("No option number or queue available.");
                    }



                }

                if (empty($digit) && $digit == "") {
                        if($hasQueue) {
                            $agiClient->streamFile($menu->media);
                            $this->info("Priority menu has no options. Redirecting to $queueName context.");
                            $client = new ClientImpl($this->getAMIOptions());
                            $redirectAction = new RedirectAction($channel, $extension, $queueName, 1);
                            $client->open();
                            $client->send($redirectAction);
                            $client->close();
                            return;
                        }

                    $this->info("User did not press any digit. Asking user to press a key to continue.");
                    $agiClient->streamFile('ivr/press-digit');
                    continue;
                }

                /*
                $menuOptions = $this->processUserInput($menuId, $digit, $agiClient, $channel, $extension);

                if ($menuOptions && $digit === '0') {
                    $nextMenu = $this->fetchMenu($menuOptions, null);

                    $agiClient->streamFile($nextMenu->media);
                    $this->info("$nextMenu->media");
                    break;
                }
                */

                $nextMenuId = $this->processUserInput($menuId, $digit, $agiClient, $channel, $extension);

                if ($nextMenuId === null) {
                    $agiClient->streamFile('invalid');
                } else {
                    $menuId = $nextMenuId;
                }
            } while ($menuId);

            $agiClient->exec('hangup');
        } catch (ChannelDownException $e) {
            $this->error("Channel down: {$e->getMessage()}");
        } catch (\Exception $e) {
            $this->error("Error processing IVR: {$e->getMessage()}");
        }
    }

    protected function fetchMenuByPriority($priority)
    {
        return DB::table('menus')->where('priority', $priority)->first();
    }

    protected function fetchMenu($menuId=null, $options=null)
    {
        $result = DB::table('menus');

        if($menuId != null) {
            $result = $result->where('id', $menuId);
        }
        else if($options != null) {
            $result = $result->where('off_working', $options);
        }

        return $result->first();
    }

    protected function processUserInput($menuId, $input, $agiClient, $channel, $extension)
    {
        // Fetch the option based on menu ID and input
        $option = DB::table('menu_options')->where('menu_id', $menuId)->where('option_number', $input)->first();

        if ($option) {
            // If target_menu_id is null, check the queue and redirect to the context
            if (is_null($option->target_menu_id)) {
                $queueContext = $option->queue;


                if (!empty($queueContext)) {

                    $client = new ClientImpl($this->getAMIOptions());
                    //$action = new RedirectAction($channel,"2138341019",$queueContext,1);
                    $action = new RedirectAction($channel, $extension, $queueContext, 1);
                    $client->open();
                    $response = $client->send($action);
                    $client->close();
                    return $response->getMessage();
                }
            }

            // Return the target_menu_id if it exists
            return $option->target_menu_id;
        }

        // Return null if no matching option is found
        return null;
    }


    protected function fetchMenuOptions($menuId)
    {
        return DB::table('menu_options')->where('menu_id', $menuId)->get();
    }

    private function checkTimings()
    {
        $settings = IvrMenuSetting::query()->first();

        if(!$settings) {
            return 3;
        }

        $now = Carbon::now();
        $start = Carbon::parse($settings->start);
        $end = Carbon::parse($settings->end);

        if($now->between($start, $end)) {
            return 1;
        }

        return 0;
    }
}