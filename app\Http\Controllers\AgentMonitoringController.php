<?php

namespace App\Http\Controllers;

use App\Models\PauseReason;
use App\Models\Queue;
use App\Models\QueueLog;
use App\Models\SystemSetting;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use PAMI\Client\Impl\ClientImpl;
use PAMI\Message\Action\QueuePauseAction;
use PAMI\Message\Action\QueueRemoveAction;
use PAMI\Message\Action\QueueStatusAction;
use PAMI\Message\Action\QueueSummaryAction;
use PAMI\Message\Event\QueueMemberEvent;
use PAMI\Message\Event\QueueMemberStatusEvent;
use PAMI\Message\Event\QueueParamsEvent;
use PAMI\Message\Event\QueueSummaryEvent;
use PAMI\Message\Action\QueueUnpauseAction;


class AgentMonitoringController extends Controller
{
    public function getBreaksForMonitoring(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            return response()->json(PauseReason::query()->get()->pluck('name'));
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getQueuesForMonitoring(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            return response()->json(Queue::query()->get()->pluck('name'));
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getMonitoringData(Queue $queue): \Illuminate\Http\JsonResponse
    {
        try {
            $action = new QueueStatusAction($queue->name);
            $client = new ClientImpl($this->getOptions());
            $client->open();
            $response = $client->send($action);
            $client->close();
            $events = $response->getEvents();
            $dataEvent = [];
            foreach ($events as $event) {
                if($event instanceof QueueParamsEvent) {
                    return response()->json($event->getKeys());
                }
            }
            return response()->json($dataEvent);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }
    public function getSummaryData(Queue $queue): \Illuminate\Http\JsonResponse
    {
        try {
            $action = new QueueSummaryAction($queue->name);
            $client = new ClientImpl($this->getOptions());
            $client->open();
            $response = $client->send($action);
            $client->close();
            $events = $response->getEvents();
            foreach ($events as $event) {
                if($event instanceof QueueSummaryEvent) {
                    return response()->json($event->getKeys());
                }
            }
            return response()->json([]);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }
    private function getPauseStatus($value)
    {


        if ($value->getKey('paused') == "1") {

            $paused = QueueLog::query()->where('queuename', $value->getKey('queue'))->where('Agent', $value->getKey('name'))->where('Event', 'PAUSE')->orderBy('time', 'desc')->limit(1)->pluck("time")->first();

            return Carbon::parse($paused)->format('Y-m-d H:i:s');
        } else {

            return Carbon::now()->format('Y-m-d 00:00:00');
        }
    }
    private function getOnCallStatus($value)
    {
        $CURRENTDATE = Carbon::now()->format('Y-m-d');

        if ($value->getKey('incall') == "1") {

            $onCall = QueueLog::query()->where('queuename', $value->getKey('queue'))->where('Agent', $value->getKey('name'))->where('Event', 'CONNECT')->orderBy('time', 'desc')->limit(1)->pluck('time')->first();
            if ($onCall == null) {
                return Carbon::now()->format('Y-m-d 00:00:00');
            } else {

                return Carbon::parse($onCall)->format('Y-m-d H:i:s');
            }
        } else {

            $onCall = QueueLog::query()->where('queuename', $value->getKey('queue'))->where('Agent', $value->getKey('name'))->where(DB::raw('Date(time)'), $CURRENTDATE)->whereIn('Event', ['COMPLETEAGENT', 'COMPLETECALLER'])->orderBy('time', 'desc')->limit(1)->pluck('time')->first();

            if ($onCall == null) {
                $onCall = QueueLog::query()->where('queuename', $value->getKey('queue'))->where('Agent', $value->getKey('name'))->where(DB::raw('Date(time)'), $CURRENTDATE)->where('Event', 'ADDMEMBER')->orderBy('time', 'desc')->limit(1)->pluck('time')->first();

                return Carbon::parse($onCall)->format('Y-m-d H:i:s');
            } else {

                return Carbon::parse($onCall)->format('Y-m-d H:i:s');
            }
        }
    }

    public function getAgentData(Queue $queue): \Illuminate\Http\JsonResponse
    {
        try {
            $action = new QueueStatusAction($queue->name);
            $client = new ClientImpl($this->getOptions());
            $client->open();
            $response = $client->send($action);
            $client->close();
            $event = $response->getEvents();
            $data = [];
            foreach ($event as $item) {
                if($item instanceof QueueMemberEvent) {
                    $data[] = [
                        'agentId' => explode("/", $item->getKey('stateinterface'))[1],
                        'agentInterface' => $item->getKey('stateinterface'),
                        'name' => $item->getKey('name'),
                        'status' => $this->getStatusMap($item->getKey('status')),
                        'pauseStatus' => $item->getKey('paused'),
                        'incall' => $item->getKey('incall'),
                        'pauseReason' => $item->getKey('pausedreason'),
                        'queue' => $item->getKey('queue'),
                        'onBreak' => $this->getPauseStatus($item),
                        'onCall' => $this->getOnCallStatus($item),
                    ];
                }
            }
            return response()->json($data);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function logoutAgentFromQueue(Queue $queue, Request $request): \Illuminate\Http\JsonResponse
    {
        $agent = $request->agent;
        try {
            $client = new ClientImpl($this->getOptions());
            $client->open();
            $this->check_agent_pause($client, $queue->name, $agent);
            $action = new QueueRemoveAction($queue->name, $agent);
            $response = $client->send($action);
            $client->close();
            return response()->json($response->getMessage());
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function sendAgentOnBreak(Queue $queue, Request $request): \Illuminate\Http\JsonResponse
    {
        $agent = $request->agent;
        $break = $request->break;
        try {
            if(!$this->checkQueueMemberStatus($queue->name, $agent)) {
                $action = new QueuePauseAction($agent, $queue->name, $break);
                $client = new ClientImpl($this->getOptions());
                $client->open();
                $response = $client->send($action);
                $client->close();
                return response()->json($response->getMessage());
            } else {
                return response()->json("Queue member already paused.");
            }
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    /**
     * @throws \PAMI\Client\Exception\ClientException
     */
    private function checkQueueMemberStatus($queue, $member): bool
    {
        $checkAction = new QueueStatusAction($queue, $member);
        $client = new ClientImpl($this->getOptions());
        $client->open();
        $response = $client->send($checkAction);
        $client->close();
        $events = $response->getEvents();
        foreach ($events as $event) {
            if($event instanceof QueueMemberEvent) {
                return $event->getKey('paused') === "1";
            }
        }
        return false;
    }
    public function getAgentLogs(Queue $queue, Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $agent = $request->agent;
            $queue = $queue->name;
            $logs = QueueLog::query()->where('queuename', $queue)->where('Agent', $agent)->orderBy('time', 'desc')->limit(10)->get(['time', 'Event', 'data1'])->toArray();
            return response()->json($logs);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }
    public function getLastLoginOfAgent(Queue $queue, Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $agent = $request->agent;
            $queue = $queue->name;
            $lastLogin = QueueLog::query()->where('queuename', $queue)->where('Agent', $agent)->where('Event', 'ADDMEMBER')->orderBy('time', 'desc')->limit(1)->first();
            return response()->json(Carbon::parse($lastLogin->time)->format('h:i:s'));
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }
    public function getSystemSettings(): \Illuminate\Http\JsonResponse
    {
        try{
            return response()->json(SystemSetting::GetSettings());
        } catch (\Exception $exception){
            return response()->json($exception->getMessage(), 500);
        }
    }
    private function getOptions(): array
    {
        return [
            'host' => SystemSetting::GetSetting('server_address'),
            'scheme' => 'tcp://',
            'port' => SystemSetting::GetSetting('manager_port'),
            'username' => SystemSetting::GetSetting('username'),
            'secret' => SystemSetting::GetSetting('secret'),
            'connect_timeout' => SystemSetting::GetSetting('connection_timeout'),
            'read_timeout' => SystemSetting::GetSetting('read_timeout')
        ];
        /*return [
            'host' => '***************',
            'scheme' => 'tcp://',
            'port' => '5038',
            'username' => 'defaultapp',
            'secret' => 'randomsecretstring',
            'connect_timeout' => 1000,
            'read_timeout' => 1000
        ];*/
    }
    private function getStatusMap($status = 0): string
    {
        switch ($status) {
            default:
                return "UNKNOWN";
            case '1':
                return "NOT_INUSE";
            case '2':
                return "INUSE";
            case '3':
                return "BUSY";
            case '4':
                return "INVALID";
            case '5':
                return "UNAVAILABLE";
            case '6':
                return "RINGING";
            case '7':
                return "RINGINUSE";
            case '8':
                return "ONHOLD";
        }
    }

    public function monitoringDashBoardData(Request $request)
    {

        $getQueues =  Queue::get('name');
        $members = 0;
        $paused = 0;
        $busy = 0;
        $idle = 0;
        $data = [];
        $data2 = [];
        $queueSummary = [];
        $quecount = count($getQueues);

        foreach ($getQueues as  $queue) {

            $client = new ClientImpl($this->getOptions());
            $action = new QueueStatusAction($queue->name);
            $action2 = new QueueSummaryAction($queue->name);
            $client->open();
            $response = $client->send($action);
            $response2 = $client->send($action2);
            $client->close();
            $events = $response->getEvents();
            $events2 = $response2->getEvents();
            $members = 0;
            $paused = 0;
            $busy = 0;
            $idle = 0;
            foreach ($events as $key => $event) {
                if ($event instanceof QueueParamsEvent) {
                    $data['queue_params'] = $event->getKeys();
                    $data['queue_params']['totalcalls'] = (int)$event->getKey('completed') + (int)$event->getKey('abandoned');
                    $data['queue_params']['holdtime'] =  $event->getKey('holdtime');
                    $data['queue_params']['talktime'] =  $event->getKey('talktime');
                } elseif ($event instanceof QueueMemberEvent) {

                    $members++;


                    if ($event->getKey('paused') == "1") {
                        $paused++;
                    } elseif ($event->getKey('incall') == "1") {
                        $busy++;
                    } else {
                        $idle++;
                    }

                    $data['agents'][$key] = $event->getKeys();
                    $data['agents'][$key]['lastcall'] = $this->parse_time($event->getKey('lastcall'));
                    $data['agents'][$key]['lastpause'] = $this->parse_time($event->getKey('lastpause'));
                    $data['agents'][$key]['status'] = $this->parse_agent_state($event->getKey('status'));
                }
            }

            foreach ($events2 as $event2) {
                if ($event2 instanceof QueueSummaryEvent) {
                    $data['queue_params2'] = $event2->getKeys();
                    $data['queue_params2']['longestholdtime'] =  $event2->getKey('longestholdtime');
                    $data['queue_params2']['total'] = $members;
                    $data['queue_params2']['paused'] = $paused;
                    $data['queue_params2']['idle'] = $idle;
                    $data['queue_params2']['busy'] = $busy;
                }
            }

            $data2[] = $data;
        }
        $totalCalls = 0;
        $completed = 0;
        $abandoned = 0;
        $total = 0;
        $idle = 0;
        $busy = 0;
        $paused = 0;
        $calls = 0;
        $sla = 0;
        $holdtime = 0;
        $talkTime = 0;
        $longestholdtime = 0;
        foreach ($data2 as $key => $values) {
            $totalCalls += $values['queue_params']['totalcalls'];
            $completed += $values['queue_params']['completed'];
            $abandoned += $values['queue_params']['abandoned'];
            $total += $values['queue_params2']['total'];
            $idle += $values['queue_params2']['idle'];
            $busy += $values['queue_params2']['busy'];
            $paused += $values['queue_params2']['paused'];
            $calls += $values['queue_params']['calls'];
            $holdtime += (int)$values['queue_params']['holdtime'] / $quecount;
            $talkTime += (int)$values['queue_params']['talktime'] / $quecount;
            $longestholdtime += (int)$values['queue_params2']['longestholdtime'] / $quecount;
            $sla += (int)$values['queue_params']['servicelevelperf2'] / $quecount;
        }

        $queueSummary = [
            'totalcalls' => $totalCalls, 'completed' => $completed, 'abandoned' => $abandoned,
            'total' => $total, 'idle' => $idle, 'busy' => $busy, 'paused' => $paused,
            'calls' => $calls, 'servicelevelperf2' => $sla, 'talktime' => gmdate("H:i:s", $talkTime),
            'holdtime' => gmdate("H:i:s", $holdtime), 'longestholdtime' => gmdate("H:i:s", $longestholdtime)
        ];



        return response()->json($queueSummary);
    }
    public function getPerQueueData(Request $request)
    {

        $getQueues =  Queue::get('name');
        $data = [];
        $data2 = [];

        foreach ($getQueues as  $queue) {

            $client = new ClientImpl($this->getOptions());
            $action = new QueueStatusAction($queue->name);
            $action2 = new QueueSummaryAction($queue->name);
            $client->open();
            $response = $client->send($action);

            $client->close();
            $events = $response->getEvents();


            foreach ($events as $key => $event) {
                if ($event instanceof QueueMemberEvent) {
                    $data['agents'] = $event->getKeys();
                    $data['agents']['lastcall'] = $this->parse_time($event->getKey('lastcall'));
                    $data['agents']['lastpause'] = $this->parse_time($event->getKey('lastpause'));
                    $data['agents']['status'] = $this->parse_agent_state($event->getKey('status'));
                }
            }

            $data2[] = $data;
        }

        return response()->json($data2);
    }
    public function getSummaryDataQwise(Queue $queue): \Illuminate\Http\JsonResponse
    {
        try {
            $data = [];
            $getQueues =  Queue::get('name');
            foreach ($getQueues as $queue) {
                # code...
                $action = new QueueSummaryAction($queue->name);
                $client = new ClientImpl($this->getOptions());
                $client->open();
                $response = $client->send($action);
                $client->close();
                $events = $response->getEvents();
                foreach ($events as $item) {
                    if ($item instanceof QueueSummaryEvent) {
                        $data[] = [

                            'queue' => $item->getKey('queue'),
                            'loggedin' => $item->getKey('loggedin'),
                            'callers' => $item->getKey('callers'),

                        ];
                    }
                }
            }
            return response()->json($data);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }
    public function getAgentDataQwise(): \Illuminate\Http\JsonResponse
    {
        try {
            $data2 = [];
            $getQueues =  Queue::get('name');

            foreach ($getQueues as $queue) {

                $action = new QueueStatusAction($queue->name);
                $client = new ClientImpl($this->getOptions());
                $client->open();
                $response = $client->send($action);
                $client->close();
                $event = $response->getEvents();
                foreach ($event as $item) {
                    if ($item instanceof QueueMemberEvent) {
                        $data2[] = [
                            'agentId' => explode("/", $item->getKey('stateinterface'))[1],
                            'agentInterface' => $item->getKey('stateinterface'),
                            'name' => $item->getKey('name'),
                            'status' => $this->getStatusMap($item->getKey('status')),
                            'pauseStatus' => $item->getKey('paused'),
                            'incall' => $item->getKey('incall'),
                            'pauseReason' => $item->getKey('pausedreason'),
                            'queue' => $item->getKey('queue'),
                            'onBreak' => $this->getPauseStatus($item),
                            'onCall' => $this->getOnCallStatus($item),

                        ];
                    }
                }
            }
            return response()->json($data2);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    private function check_agent_pause($client, $queue, $interface)
    {
        $action = new QueueStatusAction($queue, $interface);
        $response = $client->send($action);
        $status = $response->getEvents()[1]->getKey('paused') ?? 0;
        if($status == 1) {
            $action = new QueueUnpauseAction($interface, $queue, $response->getEvents()[1]->getKey('pausedreason'));
            $res = $client->send($action);
        }
    }
}
