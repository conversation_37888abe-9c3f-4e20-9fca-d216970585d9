<?php

namespace App\Imports;

use App\Models\Campaign;
use App\Models\CampaignNumber;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Model;



class CampaignNumbersImport implements ToModel, WithHeadingRow
{
    private $file;
    private $campaign;

    public function __construct($file, Campaign $campaign)
    {
        $this->file = $file;
        $this->campaign = $campaign;
    }

    /**
    * @param array $row
    *
    * @return \Illuminate\Database\Eloquent\Model
     */

    // public function model(array $row): \Illuminate\Database\Eloquent\Model
    public function model(array $row): ?Model
    {
        try {

            if (!isset($row['number']) || trim($row['number']) === '') {
                \Log::info('Skipping empty number row', ['row' => $row]);
                return null;
            }

            return $this->campaign->campaign_numbers()->create([
                'number' => trim($row['number']),
                'name' => $row['name'] ?? null,
                'attempts' => $row['attempts'] ?? '',
                'city' => $row['city'] ?? null,
                'ride_number' => $row['ride_number'] ?? '',
                'file' => $this->file,
            ]);

        } catch (\Throwable $e) {
            \Log::error('Error inserting row', [
                'message' => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
                'row' => $row,
            ]);
            return null;
        }
    }

}
