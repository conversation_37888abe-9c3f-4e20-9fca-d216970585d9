<?php

namespace App\Console\Commands;

use App\Models\PauseReason;
use App\Models\QueueLog;
use App\Models\SystemSetting;
use App\Models\User;
use App\Models\WorkCode;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use PAMI\Client\Impl\ClientImpl;
use PAMI\Message\Action\QueueStatusAction;
use PAMI\Message\Event\QueueMemberEvent;
use PAMI\Message\Action\QueueSummaryAction;
use PAMI\Message\Action\CoreShowChannelsAction;
use PAMI\Message\Event\QueueParamsEvent;
use PAMI\Message\Event\QueueSummaryEvent;
use App\Events\DashboardOutbond;
use App\Events\DashboardOutbondAgent;
use App\Events\DashboardAgentQueue;
use App\Events\DashboardDataQueue;
use App\Events\AgentLogin;
use App\Events\AbandonCallReport;
use App\Events\AgentStatus;
use App\Events\IsReady;
use App\Events\GetWorkCode;
use App\Events\GetPauseReason;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

class PollingData extends Command
{
    protected $signature = 'pollingdata:start';
    protected $description = 'Get real-time data for socket';

    public function __construct()
    {
        parent::__construct();
    }
    protected function getOptions(): array
    {
        return [
            'host' => SystemSetting::GetSetting('server_address'),
            'scheme' => 'tcp://',
            'port' => SystemSetting::GetSetting('manager_port'),
            'username' => SystemSetting::GetSetting('username'),
            'secret' => SystemSetting::GetSetting('secret'),
            'connect_timeout' => SystemSetting::GetSetting('connection_timeout'),
            'read_timeout' => SystemSetting::GetSetting('read_timeout')
        ];
    }

    public function handle()
    {
        $this->info("POLLING DATA RUNNING");

        while (true) {
            // Get all unique user queues from cache
            $userQueues = $this->getUserQueues();

            $newChanges = DB::table('cdr_changes')
                ->where('changed_at', '>=', Carbon::now()->subSeconds(10))
                ->get();

            if ($newChanges->isNotEmpty()) {
                $this->info("DISPATCH START");

                $outboundData = $this->getStats();
                $outboundAgentData = $this->getAgentStats();

                broadcast(new DashboardOutbond($outboundData));
                broadcast(new DashboardOutbondAgent($outboundAgentData));

                $this->info("DISPATCH COMPLETE");
            }

            // Process each unique queue
            foreach ($userQueues as $queue) {
                $this->info("Processing Queue: {$queue}");

                $agentQueueWiseData = $this->get_agents_data($queue);
                $agentDashboardQueueWiseData = $this->get_dashboard_data($queue);

                broadcast(new DashboardAgentQueue($agentQueueWiseData));
                broadcast(new DashboardDataQueue($agentDashboardQueueWiseData));
            }

            sleep(1);
        }
    }

    /**
     * Get all unique queues that users have selected
     */
    private function getUserQueues(): array
    {
        // Get logged in users and their queues
        $loggedInUsers = Cache::get('logged_in_users', []);
        $queues = [];

        foreach ($loggedInUsers as $userId => $userData) {
            $userQueue = Cache::get("user_{$userId}_queue");
            if ($userQueue && !in_array($userQueue, $queues)) {
                $queues[] = $userQueue;
            }
        }

        // If no user queues found, use default
        if (empty($queues)) {
            $queues[] = '100';
        }

        return $queues;
    }

    public function getStats()
    {
        try {
            $query1 = DB::select("SELECT COUNT(*) as 'total_dialed_calls', SUM(IF(disposition = 'ANSWERED', 1, 0)) as 'total_answered_calls', Round(SUM(IF(disposition = 'ANSWERED', billsec, 0)) / 60, 2) as 'total_talk_time', ROUND((SUM(IF(disposition = 'ANSWERED', billsec, 0))/60)/COUNT(*), 2) as 'avg_talk_time', Round(SUM(duration) / 60 , 2) as 'total_dialed_duration', CONCAT(ROUND(SUM(IF(disposition = 'ANSWERED', 1, 0))/COUNT(*) * 100, 2), '%') as 'answered_rate', SEC_TO_TIME(ROUND(SUM(duration) / COUNT(*))) as 'avg_call_duration' FROM `cdr` WHERE accountcode = 'Outbound' AND date(`start`) = CURRENT_DATE");
            $query2 = DB::select("SELECT Round(COUNT(*)/(SELECT COUNT(*) from ps_endpoints), 2) as 'avg_dialed_per_agent', Round(SUM(IF(disposition = 'ANSWERED', 1, 0))/(SELECT COUNT(*) from ps_endpoints),2) as 'avg_answered_per_agent' FROM `cdr` join ps_endpoints as ps on cdr.channel like CONCAT('%', ps.id, '%') WHERE date(`start`) = CURRENT_DATE");
            $data = array_merge((array) $query1[0], (array) $query2[0]);

            return $data;
        } catch (\Exception $exception) {
            $this->error($exception->getMessage());
            return [];
        }
    }

    public function getAgentStats()
    {
        try {
            $query = DB::select("SELECT ps.id, us.name, count(*) as total_call, SUM(IF(disposition = 'ANSWERED', 1, 0)) as 'total_answered_calls', SEC_TO_TIME(SUM(duration)) as 'total_dialed_duration', SEC_TO_TIME(SUM(billsec)) as 'total_answered_duration', MAX(start) as 'last_dialed_call' FROM `cdr` join ps_endpoints as ps on cdr.channel like CONCAT('%PJSIP/', ps.id, '%') inner join users as us on ps.id = us.auth_username WHERE date(`start`) = CURRENT_DATE GROUP BY ps.id ORDER BY start desc");

            return $query;
        } catch (\Exception $exception) {
            $this->error($exception->getMessage());
            return [];
        }
    }

    public function get_agents_data($queue)
    {
        $client = new ClientImpl($this->getOptions());
        $action = new QueueStatusAction($queue);
        try {
            $client->open();
            $response = $client->send($action);
            $client->close();
            $events = [];
            $oKey = 0;
            foreach ($response->getEvents() as $key => $event) {
                if ($event instanceof QueueMemberEvent) {
                    $events[$oKey] = $event->getKeys();
                    $events[$oKey]['agentId'] = explode("/", $event->getKey('stateinterface'))[1];
                    $events[$oKey]['lastcall'] = $event->getKey('lastcall') !== "0" ? $this->parse_time($event->getKey('lastcall')) : "N/A";
                    $events[$oKey]['lastpause'] = $event->getKey('lastpause') !== "0" ? $this->parse_time($event->getKey('lastpause')) : "N/A";
                    $events[$oKey]['status'] = $this->parse_agent_state((int) $event->getKey('status'));
                    $events[$oKey]['connected'] = $this->get_data($event->getKey('stateinterface'))["connectedlinenum"];
                    $events[$oKey]['application'] = $this->get_data($event->getKey('stateinterface'))["application"];
                    $oKey++;
                }
            }
            return $events;
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    private function parse_time($data)
    {
        return Carbon::createFromTimestamp($data)->format('d-m-Y h:i:s A');
    }

    // private function parse_agent_state(int $state)
    // {
    //     switch ($state) {
    //         default:
    //             return $state;
    //         case 0:
    //             return 'DEVICE_UNKNOWN';
    //         case 1:
    //             return 'DEVICE_NOT_INUSE';
    //         case 2:
    //             return 'DEVICE_INUSE';
    //         case 3:
    //             return 'DEVICE_BUSY';
    //         case 4:
    //             return 'DEVICE_INVALID';
    //         case 5:
    //             return 'DEVICE_UNAVAILABLE';
    //         case 6:
    //             return 'DEVICE_RINGING';
    //         case 7:
    //             return 'DEVICE_RINGINUSE';
    //         case 8:
    //             return 'DEVICE_ONHOLD';

    //     }
    // }

    private function parse_agent_state(int $state)
    {
        switch ($state) {
            default:
                return $state;
            case 0:
                return 'UNKNOWN';
            case 1:
                return 'IDLE'; //'DEVICE_NOT_INUSE'
            case 2:
                return 'ON-CALL'; //'DEVICE_INUSE'
            case 3:
                return 'BUSY';
            case 4:
                return 'INVALID';
            case 5:
                return 'UNAVAILABLE';
            case 6:
                return 'RINGING';
            case 7:
                return 'RINGINUSE';
            case 8:
                return 'ON HOLD';
        }
    }


    private function get_data(string $interface)
    {
        $client = new ClientImpl($this->getOptions());
        $action = new CoreShowChannelsAction();
        try {
            $client->open();
            $response = $client->send($action);
            $client->close();
            $events = $response->getEvents();
            $data = [];
            $data['application'] = null;
            $data['connectedlinenum'] = null;
            foreach ($events as $event) {
                if ($event instanceof CoreShowChannelEvent && Str::contains($event->getKey('channel'), $interface)) {
                    $data['application'] = $event->getKey('application');
                    $data['connectedlinenum'] = $event->getKey('connectedlinenum');
                }
            }
            return $data;
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    public function get_dashboard_data($queue)
    {
        $client = new ClientImpl($this->getOptions());
        $action = new QueueStatusAction($queue);
        $action2 = new QueueSummaryAction($queue);
        try {
            $client->open();
            $response = $client->send($action);
            $response2 = $client->send($action2);
            $client->close();
            $events = $response->getEvents();
            $events2 = $response2->getEvents();
            $members = 0;
            $paused = 0;
            $busy = 0;
            $idle = 0;
            $data = [];

            $date = Carbon::now();
            // $waitTime = QueueLog::query()
            //     ->from('queue_log')
            //     ->whereIn('EVENT', ['CONNECT'])
            //     ->where('queuename', '=', $queue)
            //     ->whereDate('time', '>=', $date->startOfDay())
            //     ->whereDate('time', '<=', $date->endOfDay())
            //     ->select(DB::raw("SUM(data1) as waittime"))
            //     ->value('waittime');

            foreach ($events as $key => $event) {
                if ($event instanceof QueueParamsEvent) {
                    $data['queue_params'] = $event->getKeys();
                    $data['queue_params']['totalcalls'] = (int) $event->getKey('completed') + (int) $event->getKey('abandoned');
                    $data['queue_params']['holdtime'] = gmdate("H:i:s", $event->getKey('holdtime'));
                    $data['queue_params']['talktime'] = gmdate("H:i:s", $event->getKey('talktime'));

                    $totalCalls = $data['queue_params']['totalcalls'];
                    $totalTalkTime = $event->getKey('talktime');
                    $totalHoldTime = $event->getKey('holdtime');

                    $data['queue_params']['avgHandlingTime'] = $totalCalls > 0 ? gmdate("H:i:s", ($totalTalkTime + $totalHoldTime) / $totalCalls) : '00:00:00';
                } elseif ($event instanceof QueueMemberEvent) {

                    $members++;

                    if ($event->getKey('paused') == "1") {
                        $paused++;
                    } elseif ($event->getKey('incall') == "1") {
                        $busy++;
                    } else {
                        $idle++;
                    }

                    $data['agents'][$key] = $event->getKeys();
                    $data['agents'][$key]['lastcall'] = $this->parse_time($event->getKey('lastcall'));
                    $data['agents'][$key]['lastpause'] = $this->parse_time($event->getKey('lastpause'));
                    $data['agents'][$key]['status'] = $this->parse_agent_state($event->getKey('status'));
                }
            }

            foreach ($events2 as $event2) {
                if ($event2 instanceof QueueSummaryEvent) {
                    $data['queue_params2'] = $event2->getKeys();
                    $data['queue_params2']['longestholdtime'] = gmdate("H:i:s", $event2->getKey('longestholdtime'));
                    $data['queue_params2']['total'] = $members;
                    $data['queue_params2']['paused'] = $paused;
                    $data['queue_params2']['idle'] = $idle;
                    $data['queue_params2']['busy'] = $busy;
                }
            }

            return $data;
        } catch (\Exception $exception) {
            return $exception->getMessage();
        }
    }
}
