<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;

class RingNoAnswerAgentController extends Controller
{
    public function getDataBKP(Request $request): \Illuminate\Http\JsonResponse
    {
        /**
         * Query: SELECT a.Agent as 'agent', SUM(a.data1)/1000 as 'ringtime', COUNT(*) as 'number_of_calls'
         * from queue_log a join queue_log b on a.callid = b.callid and b.Event = 'ENTERQUEUE'
         * where a.Event = 'RINGNOANSWER' group by a.Agent
         */

        $date = [];
        $filter = "";

        if (isset($request->date)) {
            $date[0] = Carbon::parse($request->date[0])->toDateString();
            $date[1] = Carbon::parse($request->date[1])->toDateString();
            $filter .= "and date(a.time) >= '{$date[0]}' and date(a.time) <= '{$date[1]}'";
        } else {
            $date[0] = Carbon::now()->toDateString();
            $filter .= "and date(a.time) = '{$date[0]}'";
        }

        //old 
        // if (isset($request->agents) && is_array($request->agents)) {
        //     $agents = implode("','", $request->agents);
        //     $filter .= " and a.Agent IN ('{$agents}')";
        // }

        //new 
        if ($request->has('agent') && is_array($request->agent)) {
            $agentList = array_map(function ($agent) {
                return "'" . addslashes($agent) . "'";
            }, $request->agent);
        
            $agents = implode(",", $agentList); 
            $filter .= " AND a.Agent IN ({$agents})";
        }
        

        try {
            $data = DB::select("SELECT a.Agent as 'agent', SUM(a.data1)/1000 as 'ringtime', COUNT(*) as 'number_of_calls'
                            from queue_log a
                            join queue_log b on a.callid = b.callid and b.Event = 'ENTERQUEUE'
                            where a.Event = 'RINGNOANSWER' {$filter}
                            group by a.Agent");
            return response()->json($data);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getData(Request $request): \Illuminate\Http\JsonResponse
    {
        /**
         * Query: SELECT a.Agent as 'agent', SUM(a.data1)/1000 as 'ringtime', COUNT(*) as 'number_of_calls'
         * from queue_log a join queue_log b on a.callid = b.callid and b.Event = 'ENTERQUEUE'
         * where a.Event = 'RINGNOANSWER' group by a.Agent
         */

        $date = [];
        $filter = "";

        if (isset($request->date)) {
            $date[0] = Carbon::parse($request->date[0])->toDateString();
            $date[1] = Carbon::parse($request->date[1])->toDateString();
            $filter .= "and date(a.time) >= '{$date[0]}' and date(a.time) <= '{$date[1]}'";
        } else {
            $date[0] = Carbon::now()->toDateString();
            $filter .= "and date(a.time) = '{$date[0]}'";
        }

        if (isset($request->agents) && is_array($request->agents)) {
            $agents = implode("','", $request->agents);
            $filter .= " and a.Agent IN ('{$agents}')";
        }

        try {
            $data = DB::select("SELECT a.Agent as 'agent', SUM(a.data1)/1000 as 'ringtime', COUNT(*) as 'number_of_calls'
                            from queue_log a
                            join queue_log b on a.callid = b.callid and b.Event = 'ENTERQUEUE'
                            where a.Event = 'RINGNOANSWER' {$filter}
                            group by a.Agent");
            return response()->json($data);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

}
