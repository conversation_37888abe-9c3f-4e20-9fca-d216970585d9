<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\RingNoAnswerExport;

class RingNoAnswerReportController extends Controller
{
    public function getData(Request $request): \Illuminate\Http\JsonResponse
    {
        $request->validate([
            'queue' => ['nullable', 'exists:queues,name'],
            'agent' => ['nullable', 'exists:users,name']
        ]);

        $pageSize = $request->pageSize ?? 10;
        $page = $request->page ?? 1;
        $date = [];
        $filter = "";

        try {
            //$count = DB::select("COUNT(*) as total  from queue_log a join queue_log b on a.callid = b.callid and b.Event = 'ENTERQUEUE' where a.Event = 'RINGNOANSWER' {$filter}");
            //$data = DB::select("SELECT a.time as 'time', b.data2 as 'partya', a.queuename as 'queue', a.Agent as 'agent', a.data1/1000 as 'ringtime' from queue_log a join queue_log b on a.callid = b.callid and b.Event = 'ENTERQUEUE' where a.Event = 'RINGNOANSWER' {$filter}");
            $data = DB::table('queue_log', 'a')
                ->join('queue_log as b', function ($join) {
                    $join->on('a.callid', '=', 'b.callid')
                        ->where('b.Event', 'ENTERQUEUE');
                })
                ->select(['a.time as time', 'b.data2 as partya', 'a.queuename as queue', 'a.Agent as agent', DB::raw('a.data1/1000 as ringtime')])
                ->where('a.Event', '=', 'RINGNOANSWER');

            //            if(isset($request->date)) {
//                $date[0] = Carbon::parse($request->date[0])->toDateTimeString();
//                $date[1] = Carbon::parse($request->date[1])->toDateTimeString();
//                $data = $data->whereDate('a.time', '>=', $date[0])->whereDate('a.time', '<=', $date[1]);
//            }

            if ($request->has('date') && is_array($request->date)) {
                $start = Carbon::parse($request->date[0])->timezone('Asia/Karachi');
                $end = Carbon::parse($request->date[1])->timezone("Asia/Karachi");
                $data->whereBetween("a.time", [$start->toDateTime(), $end->toDateTime()]);
            } else {
                $start = Carbon::now()->toDateString();
                $data->whereDate('a.time', '=', $start);
            }
            if (isset($request->agent)) {
                //$filter .= "and a.agent = '{$request->agent}'";
                $data = $data->whereIn('a.agent', $request->agent);
            }

            if (isset($request->queue)) {
                //$filter .= "and a.queuename = {$request->queue}";
                $data = $data->where('a.queuename', '=', $request->queue);
            }

            if (isset($request->partya)) {
                //$filter .= "and b.data2 = {$request->partya}";
                $data = $data->where('b.data2', '=', $request->partya);
            }

            $data = $data->orderBy('a.time', 'desc')->paginate($pageSize);
            return response()->json($data);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function export(Request $request)
    {

        try {
            $data = DB::table('queue_log as a')
                ->join('queue_log as b', function ($join) {
                    $join->on('a.callid', '=', 'b.callid')
                        ->where('b.Event', 'ENTERQUEUE');
                })
                ->select([
                    'a.time as time',
                    'b.data2 as partya',
                    'a.queuename as queue',
                    'a.Agent as agent',
                    DB::raw('a.data1/1000 as ringtime')
                ])
                ->where('a.Event', '=', 'RINGNOANSWER');

            if (isset($request->date) && $request->date !== null && $request->date !== 'null') {
                $date = json_decode($request->date);
                $start = Carbon::parse($date[0])->timezone('Asia/Karachi');
                $end = Carbon::parse($date[1])->timezone("Asia/Karachi");
                $data->whereBetween("a.time", [$start->toDateTime(), $end->toDateTime()]);
            } else {
                $start = Carbon::now()->toDateString();
                $data->whereDate('a.time', '=', $start);
            }

            if (isset($request->agent) && $request->agent !== null && $request->agent !== 'null') {
                $data->whereIn('a.agent', (array) json_decode($request->agent));
            }


            if (isset($request->queue) && $request->queue !== null && $request->queue !== 'null') {
                $data->where('a.queuename', '=', $request->queue);
            }

            if (isset($request->partya) && $request->partya !== null && $request->partya !== 'null') {
                $data->where('b.data2', '=', $request->partya);
            }
            // dd($data->get());
            return Excel::download(new RingNoAnswerExport($data->get()->toArray()), 'ring-no-answer.csv');
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }


}

