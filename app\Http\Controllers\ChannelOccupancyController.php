<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ChannelOccupancyController extends Controller
{
    public function getData(Request $request): \Illuminate\Http\JsonResponse
    {
        /**
         * Query: SELECT start, COUNT(*) FROM `cdr` where start BETWEEN '2021-07-15 00:00:00' and '2021-07-15 23:59:59' group by TIMESTAMP(start) ORDER BY `start` ASC
         */
        try {
            $date = [];
            if(isset($request->date)) {
                $date[0] = $request->date[0];
                $date[1] = $request->date[1];
            } else {
                $date[0] = Carbon::now()->startOfDay();
                $date[1] = Carbon::now()->endOfDay();
            }
            $data = DB::select("SELECT start as 'datetime', COUNT(*) as 'calls' FROM `cdr` where start BETWEEN '{$date[0]}' and '{$date[1]}' group by TIMESTAMP(start) ORDER BY `start` ASC");
            return response()->json($data);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getTrunkPerHourBKP(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $date = Carbon::now();
            if(isset($request->date)) {
                $date = Carbon::parse($request->date);
            } 
            $date = $date->format('Y-m-d');
            $data = DB::select("SELECT CONCAT(CONCAT(EXTRACT(hour FROM time), ':00:00') , '-' , CONCAT((EXTRACT(hour FROM time) + 1), ':00:00')) as 'time_period', SUM(IF(EVENT = 'ABANDON', 1 , 0) + IF(EVENT = 'CONNECT', 1, 0)) AS 'entered' , SUM(IF(EVENT = 'CONNECT', 1, 0)) AS 'answered', SUM(IF(EVENT = 'ABANDON', 1 , 0)) AS 'abandoned' FROM `queue_log` where date(time) = '{$date}' GROUP by HOUR(time)");
            return response()->json($data);
        } catch (\Exception $exception){
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getTrunkPerHour(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $date = Carbon::now();
            if (isset($request->date)) {
                $date = Carbon::parse($request->date);
            }
            $date = $date->format('Y-m-d');

            $queueFilter = "";
            if (!empty($request->queue)) {
                $queueFilter = " AND queuename = '{$request->queue}'";
            }

            $query = "SELECT 
                        CONCAT(CONCAT(EXTRACT(hour FROM time), ':00:00') , '-' , 
                            CONCAT((EXTRACT(hour FROM time) + 1), ':00:00')) AS 'time_period', 
                        SUM(IF(EVENT = 'ABANDON', 1 , 0) + IF(EVENT = 'CONNECT', 1, 0)) AS 'entered', 
                        SUM(IF(EVENT = 'CONNECT', 1, 0)) AS 'answered', 
                        SUM(IF(EVENT = 'ABANDON', 1 , 0)) AS 'abandoned' 
                    FROM `queue_log` 
                    WHERE DATE(time) = '{$date}' 
                    $queueFilter
                    GROUP BY HOUR(time)";

            $data = DB::select($query);
            return response()->json($data);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getTrunkPerHourOutbound(Request $request): \Illuminate\Http\JsonResponse
    {
        try{
            $date = Carbon::now();
            if(isset($request->date)) {
                $date = Carbon::parse($request->date);
            }
            $date = $date->format('Y-m-d');
            $data = DB::select("SELECT CONCAT(CONCAT(EXTRACT(HOUR FROM START), ':00:00'),'-', CONCAT((EXTRACT(HOUR FROM START) + 1), ':00:00')) AS 'name', SUM(IF(accountcode = 'Outbound', 1, 0)) AS outbound, SUM(IF(accountcode = 'Queue', 1, 0)) AS queue FROM cdr  where date(start) = '{$date}' GROUP by HOUR(start)");
            return response()->json($data);
        } catch (\Exception $exception){
            return response()->json($exception->getMessage(), 500);
        }
    }
}
