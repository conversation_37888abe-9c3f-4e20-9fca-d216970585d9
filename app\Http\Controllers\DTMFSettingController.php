<?php

namespace App\Http\Controllers;

use App\Models\DTMFSetting;
use Illuminate\Http\Request;

class DTMFSettingController extends Controller
{
    public function index()
    {
        $dtmfSettings = DTMFSetting::all();
        return response()->json($dtmfSettings);
    }

    public function create() {}

    public function store(Request $request)
    {
        $request->validate([
            'dtmf' => 'required|unique:dtmf_settings,dtmf',
            'value' => 'required|string|unique:dtmf_settings,value',
        ]);

        $dtmfSetting = new DTMFSetting();
        $dtmfSetting->dtmf = $request->dtmf;
        $dtmfSetting->value = $request->value;

        $dtmfSetting->save();
        // 
        return response()->json($dtmfSetting);
    }


    public function show($id)
    {
        $dtmfSetting = DTMFSetting::findOrFail($id);
        return response()->json($dtmfSetting);
    }

    public function edit($id)
    {
        $dtmfSetting = DTMFSetting::findOrFail($id);
        return response()->json($dtmfSetting);
    }

    public function update(Request $request, $id)
    {
        $request->validate([
            'dtmf' => 'required|unique:dtmf_settings,dtmf,' . $id,
            'value' => 'required|string|unique:dtmf_settings,value,' . $id,
        ]);

        $dtmfSetting = DTMFSetting::findOrFail($id);
        $dtmfSetting->dtmf = $request->dtmf;
        $dtmfSetting->value = $request->value;

        $dtmfSetting->save();

        return response()->json($dtmfSetting);
    }


    public function destroy($id)
    {
        $dtmfSetting = DTMFSetting::findOrFail($id);
        $dtmfSetting->delete();

        return response()->json(['message' => 'DTMF setting deleted successfully']);
    }
}
