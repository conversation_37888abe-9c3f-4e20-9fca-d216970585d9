<?php

namespace App\Http\Controllers;

use App\Models\Cdr;
use App\Models\QueueLog;
use App\Models\SystemSetting;
use App\Models\WorkCode;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Exports\OutboundDispositionReportExport;
use Maatwebsite\Excel\Facades\Excel;

class OutboundDispositionController extends Controller
{
    /**
     * Get outbound disposition report
     * @return \Illuminate\Http\JsonResponse
     */
    public function getOutboundDisposition(): \Illuminate\Http\JsonResponse
    {
        try {
            $date = Carbon::now();
            $outboundTrunkString = SystemSetting::GetSetting('outbound_string') ?? 'TCL-endpoint';
            $cdr = Cdr::query()->whereDate('start', '>=', $date->startOfDay())->whereDate('end', '<=', $date->endOfDay())->where('dstchannel', 'like', "%$outboundTrunkString%")->join('queue_log', 'uniqueid', '=', 'queue_log.callid')->join('work_codes', 'id', '=', 'queue_log.data1')->select(['uniqueid', 'channel', 'dst', 'start', 'disposition', 'work_codes.name as workcode'])->orderBy('start', 'desc')->get();
            return response()->json($cdr);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getFilteredOutboundDisposition(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $outboundTrunkString = SystemSetting::GetSetting('outbound_string') ?? 'TCL-endpoint';
            $cdr = Cdr::query();
            $cdr->where('dstchannel', 'like', "%{$outboundTrunkString}%");
            if ($request->has('range') && is_array($request->range)) {
                $start = Carbon::parse($request->range[0])->timezone('Asia/Karachi');
                $end = Carbon::parse($request->range[1])->timezone("Asia/Karachi");
                $cdr->whereBetween("start", [$start->toDateTime(), $end->toDateTime()]);
            }
            if ($request->has('agent')) {
                $agents = $request->agent;
                if (is_array($agents)) {
                    $cdr->where(function ($query) use ($agents) {
                        foreach ($agents as $agent) {
                            $query->orWhere('channel', 'like', "%$agent%");
                        }
                    });
                } else {
                    $cdr->where('channel', 'like', "%$agents%");
                }
            }
            if ($request->has('destination'))
                $cdr->where('dst', $request->destination);
            if ($request->has('call_status'))
                $cdr->where('disposition', $request->call_status);
            $cdr->join('queue_log', 'uniqueid', '=', 'queue_log.callid')->join('work_codes', 'id', '=', 'queue_log.data1')->select(['uniqueid', 'channel', 'dst', 'start', 'disposition', 'work_codes.name as workcode','Agent'])->orderBy('start', 'desc')->get();
            return response()->json($cdr->get());
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function exportOutboundDisposition(Request $request)
    {
        try {
            $outboundTrunkString = SystemSetting::GetSetting('outbound_string') ?? 'TCL-endpoint';
            $cdr = Cdr::query();
            $cdr->where('dstchannel', 'like', "%{$outboundTrunkString}%");
            if ($request->has('range') && is_array($request->range)) {
                $start = Carbon::parse($request->range[0])->timezone('Asia/Karachi');
                $end = Carbon::parse($request->range[1])->timezone("Asia/Karachi");
                $cdr->whereBetween("start", [$start->toDateTime(), $end->toDateTime()]);
            }
            if ($request->has('agent')) {
                $agents = $request->agent;
                if (is_array($agents)) {
                    $cdr->where(function ($query) use ($agents) {
                        foreach ($agents as $agent) {
                            $query->orWhere('channel', 'like', "%$agent%");
                        }
                    });
                } else {
                    $cdr->where('channel', 'like', "%$agents%");
                }
            }
            if ($request->has('dst')) {
                $cdr->where('dst', $request->dst);
            }
            if ($request->has('call_status')) {
                $cdr->where('disposition', $request->call_status);
            }

            $cdr->join('queue_log', 'uniqueid', '=', 'queue_log.callid')->join('work_codes', 'id', '=', 'queue_log.data1')->select(['uniqueid', 'channel', 'dst', 'start', 'disposition', 'work_codes.name as workcode'])->orderBy('start', 'desc')->get();

            return Excel::download(new OutboundDispositionReportExport($cdr->get()->toArray()), 'OutboundDispositionReport.xlsx');

        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }
}
