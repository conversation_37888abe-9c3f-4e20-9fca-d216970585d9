
   INFO  Horizon started successfully.  


   INFO  Horizon started successfully.  


   INFO  Horizon started successfully.  


   INFO  Horizon started successfully.  


   INFO  Horizon started successfully.  


   Illuminate\Database\QueryException 

  SQLSTATE[HY000] [2002] No such file or directory (Connection: mysql, SQL: select * from `users` where `id` in (11, 14))

  at /home/<USER>/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829
    825▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    826▕                 );
    827▕             }
    828▕ 
  ➜ 829▕             throw new QueryException(
    830▕                 $this->getName(), $query, $this->prepareBindings($bindings), $e
    831▕             );
    832▕         }
    833▕     }

  1   [internal]:0
      App\Console\Commands\CheckAgentReadyStatus::__construct()
      [2m+22 vendor frames [22m

  24  /home/<USER>/app/Console/Commands/CheckAgentReadyStatus.php:91
      Illuminate\Database\Eloquent\Builder::get()


   Illuminate\Database\QueryException 

  SQLSTATE[HY000] [2002] No such file or directory (Connection: mysql, SQL: select * from `users` where `id` in (11, 14))

  at /home/<USER>/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829
    825▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    826▕                 );
    827▕             }
    828▕ 
  ➜ 829▕             throw new QueryException(
    830▕                 $this->getName(), $query, $this->prepareBindings($bindings), $e
    831▕             );
    832▕         }
    833▕     }

  1   [internal]:0
      App\Console\Commands\CheckAgentReadyStatus::__construct()
      [2m+22 vendor frames [22m

  24  /home/<USER>/app/Console/Commands/CheckAgentReadyStatus.php:91
      Illuminate\Database\Eloquent\Builder::get()


   Illuminate\Database\QueryException 

  SQLSTATE[HY000] [2002] No such file or directory (Connection: mysql, SQL: select * from `users` where `id` in (11, 14))

  at /home/<USER>/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829
    825▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    826▕                 );
    827▕             }
    828▕ 
  ➜ 829▕             throw new QueryException(
    830▕                 $this->getName(), $query, $this->prepareBindings($bindings), $e
    831▕             );
    832▕         }
    833▕     }

  1   [internal]:0
      App\Console\Commands\CheckAgentReadyStatus::__construct()
      [2m+22 vendor frames [22m

  24  /home/<USER>/app/Console/Commands/CheckAgentReadyStatus.php:91
      Illuminate\Database\Eloquent\Builder::get()


   INFO  Horizon started successfully.  


   INFO  Horizon started successfully.  


   INFO  Horizon started successfully.  


   INFO  Horizon started successfully.  


   Illuminate\Database\QueryException 

  SQLSTATE[HY000] [2002] No such file or directory (Connection: mysql, SQL: select * from `users` where `id` in (11, 14, 16, 6))

  at /home/<USER>/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829
    825▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    826▕                 );
    827▕             }
    828▕ 
  ➜ 829▕             throw new QueryException(
    830▕                 $this->getName(), $query, $this->prepareBindings($bindings), $e
    831▕             );
    832▕         }
    833▕     }

  1   [internal]:0
      App\Console\Commands\CheckAgentReadyStatus::__construct()
      [2m+22 vendor frames [22m

  24  /home/<USER>/app/Console/Commands/CheckAgentReadyStatus.php:91
      Illuminate\Database\Eloquent\Builder::get()


   INFO  Horizon started successfully.  


   INFO  Horizon started successfully.  


   INFO  Horizon started successfully.  


   INFO  Horizon started successfully.  


   INFO  Horizon started successfully.  


   INFO  Horizon started successfully.  


   INFO  Horizon started successfully.  


   INFO  Horizon started successfully.  


   INFO  Horizon started successfully.  


   INFO  Horizon started successfully.  


   INFO  Horizon started successfully.  

