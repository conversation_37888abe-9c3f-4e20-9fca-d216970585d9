<?php

namespace App\Http\Controllers;

use App\Models\User;
//use http\Client\Response;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use App\Models\Permission;
use App\Models\Role;
use Illuminate\Support\Facades\Session;
use Laratrust\Helper;
use function PHPUnit\Framework\isNull;
use Illuminate\Support\Facades\Auth;

class RoleController extends Controller
{
    protected $rolesModel;
    protected $permissionModel;

    public function __construct()
    {
        $this->middleware('permission:create_roles', ['only' => ['store']]);
        $this->middleware('permission:update_roles', ['only' => ['update']]);
        $this->middleware('permission:delete_roles', ['only' => ['destroy']]);
        $this->middleware('permission:read_roles', ['only' => ['index']]);

        $this->rolesModel = Config::get('laratrust.models.role');
        $this->permissionModel = Config::get('laratrust.models.permission');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    // public function index()
    // {
    //     $roles = Role::all();
    //     return response()->json($roles);
    // }

    public function index()
    {
        $authUser = Auth::user();

        if (!$authUser) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        if ($authUser->hasRole('superadministrator')) {
            $roles = Role::all();
        } else {

            $excludedRoles = ['superadministrator', 'admin'];

            $roles = Role::whereNotIn('name', $excludedRoles)->get();
        }

        return response()->json($roles);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->validate($request, [
            'name' => 'required|unique:roles,name',
        ]);
        $role = Role::create($request->all());
        if($role->name!="")
            return  response()->json("Role {$role->name} has been created.");
        else
            return  response()->json("Role {$role->name} create has been failed.");
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $role = Role::find($id);
        $rolePermissions = Permission::join("role_has_permissions","role_has_permissions.permission_id","=","permissions.id")
            ->where("role_has_permissions.role_id",$id)
            ->get();
        return view('roles.show',compact('role','rolePermissions'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $role = Role::find($id);
        $permission = Permission::get();
        $rolePermissions = DB::table("role_has_permissions")->where("role_has_permissions.role_id",$id)
            ->pluck('role_has_permissions.permission_id','role_has_permissions.permission_id')
            ->all();
        return view('roles.edit',compact('role','permission','rolePermissions'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  Role $role
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Role $role)
    {
        $this->validate($request, [
            'name' => 'required|unique:roles,name',
        ]);
        $status = $role->update($request->all());
        if($status)
            return  response()->json("Role {$role->name} has been created.");
        else
            return  response()->json("Role {$role->name} create has been failed.");
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param Role $role
     * @return \Illuminate\Http\Response
     */

    // public function assignPermission(Request $request, Permission $permission)
    // {
    //     $role = new Role();
    //     $role = $role->findOrFail((int) $request->role['id']);
    //     $role->syncPermissions($request->permission);
    //     return response()->json("Permission has been assigned");
    // }

    public function assignPermission(Request $request, Permission $permission)
    {
        $role = new Role();
        $role = $role->findOrFail((int) $request->role['id']);
        $role->syncPermissions($request->permission);

        $allowedNavbarPermissions = Permission::whereIn('name', $request->permission)
        ->pluck('name')
        ->toArray();

        // Navbar::whereIn('permission', $allowedNavbarPermissions)
        // ->update(['module_accessibility' => true]);

        return response()->json([
            'message' => 'Permission has been assigned successfully.',
            'role' => $role->name,
            'permissions' => $allowedNavbarPermissions,
        ]);
    }

    // public function assignRoleToUser(Request $request)
    // {
    //     $user= User::query()->findOrFail($request->userID);
    //     $user->syncRoles($request->roles);

    //     return response()->json("User role has been updated");
    // }

    public function assignRoleToUser(Request $request)
    {
        $authUser = Auth::user();

        if ($authUser->id == $request->userID) {

            if (!$authUser->hasRole('superadministrator')) {
                return response()->json(['error' => 'You cannot update your own role.'], 403);
            }
        }

        $user = User::query()->findOrFail($request->userID);

        $user->syncRoles($request->roles);

        return response()->json("User role has been updated");
    }

    public function getPermissionByRole(Request $request, Role $role): \Illuminate\Http\JsonResponse
    {
        $role = $this->rolesModel::query()
            ->with('permissions:id,name,display_name')
            ->findOrFail($request->roleId);

        $collection = collect($role->permissions)->map(function ($name) {
                return $name->name;
        })->reject(function ($name) {
            return empty($name);
        });

        return response()->json($collection);
    }

    function getRoleAssignToUser(Request $request)
    {
//        $modelKey = $request->get('model');
//        $userModel = Config::get('laratrust.user_models')[$modelKey] ?? null;

//        $user = User::query()->with('roles:id,name')->findOrFail($request->userID);

        $userModel = Config::get('laratrust.user_models')['users'] ?? null;
        $user = $userModel::query()
            ->with('roles:id,name')
            ->findOrFail($request->userID);

        $roles = $this->rolesModel::orderBy('name')->get(['id', 'name', 'display_name'])
            ->map(function ($role) use ($user) {
                $role->assigned = $user->roles
                    ->pluck('id')
                    ->contains($role->id);
                $role->isRemovable = Helper::roleIsRemovable($role);

                return $role;
            });
//        foreach ($user->roles as $value) {
//            if($value->name)
//                $arr[]= $value->name;
//        }
        return response()->json($roles);

//        return response()->json($roles);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  Role $role
     * @return \Illuminate\Http\Response
     */
    public function destroy(Role $role)
    {
        $status = $role->delete();
        if($status)
            return response()->json("Permisison {$role->name} has been deleted.");
        else
            return response()->json("Permission {$role->name} delete has benn failed.");
    }
}
