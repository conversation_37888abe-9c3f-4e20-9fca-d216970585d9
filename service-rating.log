[2025-04-21 17:08:28] INFO: Starting custom call routing invoke script...
[2025-04-21 17:08:46] INFO: Incoming call from ***********
array:5 [
  "file" => "/home/<USER>/vendor/marcelog/pagi/src/PAGI/Client/AbstractClient.php"
  "line" => 373
  "function" => "send"
  "class" => "PAMI\AsyncAgi\AsyncClientImpl"
  "type" => "->"
] // app/Console/Commands/CustomService.php:240
[2025-04-21 17:08:46] INFO: Exception occurred: Channel PJSIP/HostedFET238-0000000d does not exist.
[2025-04-21 17:12:13] INFO: Incoming call from ***********
array:5 [
  "file" => "/home/<USER>/vendor/marcelog/pagi/src/PAGI/Client/Result/DataReadResult.php"
  "line" => 58
  "function" => "__construct"
  "class" => "PAGI\Client\Result\DigitReadResult"
  "type" => "->"
] // app/Console/Commands/CustomService.php:240
[2025-04-21 17:12:13] INFO: Exception occurred: 
[2025-04-21 17:14:04] INFO: Incoming call from ***********
array:5 [
  "file" => "/home/<USER>/vendor/marcelog/pagi/src/PAGI/Client/Result/DataReadResult.php"
  "line" => 58
  "function" => "__construct"
  "class" => "PAGI\Client\Result\DigitReadResult"
  "type" => "->"
] // app/Console/Commands/CustomService.php:240
[2025-04-21 17:14:04] INFO: Exception occurred: 
[2025-04-21 17:14:18] INFO: Incoming call from ***********
array:5 [
  "file" => "/home/<USER>/vendor/marcelog/pagi/src/PAGI/Client/Result/DataReadResult.php"
  "line" => 58
  "function" => "__construct"
  "class" => "PAGI\Client\Result\DigitReadResult"
  "type" => "->"
] // app/Console/Commands/CustomService.php:240
[2025-04-21 17:14:18] INFO: Exception occurred: 
[2025-04-21 17:14:34] INFO: Incoming call from ***********
[2025-04-21 18:15:25] INFO: Starting custom call routing invoke script...
[2025-04-21 18:15:55] INFO: Incoming call from ***********
[2025-04-21 18:17:08] INFO: Incoming call from ***********
[2025-04-21 18:17:51] INFO: Incoming call from ***********
array:5 [
  "file" => "/home/<USER>/vendor/marcelog/pagi/src/PAGI/Client/Result/DataReadResult.php"
  "line" => 58
  "function" => "__construct"
  "class" => "PAGI\Client\Result\DigitReadResult"
  "type" => "->"
] // app/Console/Commands/CustomService.php:240
[2025-04-21 18:17:51] INFO: Exception occurred: 
[2025-04-21 18:20:37] INFO: Incoming call from ***********
[2025-04-21 18:21:19] INFO: Incoming call from ***********
[2025-04-21 18:24:24] INFO: Incoming call from ***********
[2025-04-21 18:26:45] INFO: Incoming call from ***********
array:5 [
  "file" => "/home/<USER>/vendor/marcelog/pagi/src/PAGI/Client/Result/DataReadResult.php"
  "line" => 58
  "function" => "__construct"
  "class" => "PAGI\Client\Result\DigitReadResult"
  "type" => "->"
] // app/Console/Commands/CustomService.php:240
[2025-04-21 18:26:45] INFO: Exception occurred: 
[2025-04-21 18:27:02] INFO: Incoming call from ***********
[2025-04-21 18:28:11] INFO: Incoming call from ***********
[2025-04-21 18:47:25] INFO: Incoming call from ***********
array:5 [
  "file" => "/home/<USER>/vendor/marcelog/pagi/src/PAGI/Client/Result/DataReadResult.php"
  "line" => 58
  "function" => "__construct"
  "class" => "PAGI\Client\Result\DigitReadResult"
  "type" => "->"
] // app/Console/Commands/CustomService.php:240
[2025-04-21 18:47:25] INFO: Exception occurred: 
[2025-04-21 18:47:40] INFO: Incoming call from ***********
[2025-04-21 18:48:46] INFO: Incoming call from ***********
[2025-04-21 18:49:22] INFO: Incoming call from ***********
[2025-04-21 18:49:48] INFO: Incoming call from ***********
[2025-04-21 18:50:15] INFO: Incoming call from ***********
[2025-04-22 15:45:30] INFO: Starting custom call routing invoke script...

   PAMI\Client\Exception\ClientException 

  Error reading

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:292
    288▕         // Read something.
    289▕         //$read = @fread($this->socket, 65535);
    290▕         $read = @fread($this->socket, 8192);
    291▕         if ($read === false || (empty($read) && @feof($this->socket))) {
  ➜ 292▕             throw new ClientException('Error reading');
    293▕         }
    294▕         $this->currentProcessingMessage .= $read;
    295▕         // If we have a complete message, then return it. Save the rest for
    296▕         // later.

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/CustomService.php:287
      PAMI\Client\Impl\ClientImpl::process()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()


   Illuminate\Database\QueryException 

  SQLSTATE[HY000] [2002] No such file or directory (Connection: mysql, SQL: select * from `users` where `id` in (11, 14, 16, 6))

  at /home/<USER>/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829
    825▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    826▕                 );
    827▕             }
    828▕ 
  ➜ 829▕             throw new QueryException(
    830▕                 $this->getName(), $query, $this->prepareBindings($bindings), $e
    831▕             );
    832▕         }
    833▕     }

  1   [internal]:0
      App\Console\Commands\CheckAgentReadyStatus::__construct()
      [2m+22 vendor frames [22m

  24  /home/<USER>/app/Console/Commands/CheckAgentReadyStatus.php:91
      Illuminate\Database\Eloquent\Builder::get()

[2025-04-24 15:34:14] INFO: Starting custom call routing invoke script...

   PAMI\Client\Exception\ClientException 

  error: "Connection refused" while create socket "tcp://solutionsv3.tclcontactplus.com:5038"

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:200
    196▕             STREAM_CLIENT_CONNECT,
    197▕             $this->context
    198▕         );
    199▕         if ($this->socket === false) {
  ➜ 200▕             throw new ClientException(sprintf('error: "%s" while create socket "%s"', $errstr, $socketUri));
    201▕         }
    202▕         // set socket in block mode
    203▕         if (!stream_set_blocking($this->socket, true)) {
    204▕             throw new ClientException(sprintf('error set block mode on "%s" socket', $socketUri));

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/CustomService.php:284
      PAMI\Client\Impl\ClientImpl::open()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()

[2025-04-24 15:34:16] INFO: Starting custom call routing invoke script...

   PAMI\Client\Exception\ClientException 

  Error reading

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:292
    288▕         // Read something.
    289▕         //$read = @fread($this->socket, 65535);
    290▕         $read = @fread($this->socket, 8192);
    291▕         if ($read === false || (empty($read) && @feof($this->socket))) {
  ➜ 292▕             throw new ClientException('Error reading');
    293▕         }
    294▕         $this->currentProcessingMessage .= $read;
    295▕         // If we have a complete message, then return it. Save the rest for
    296▕         // later.

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/CustomService.php:287
      PAMI\Client\Impl\ClientImpl::process()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()

[2025-04-24 17:30:28] INFO: Starting custom call routing invoke script...

   PAMI\Client\Exception\ClientException 

  error: "Connection refused" while create socket "tcp://solutionsv3.tclcontactplus.com:5038"

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:200
    196▕             STREAM_CLIENT_CONNECT,
    197▕             $this->context
    198▕         );
    199▕         if ($this->socket === false) {
  ➜ 200▕             throw new ClientException(sprintf('error: "%s" while create socket "%s"', $errstr, $socketUri));
    201▕         }
    202▕         // set socket in block mode
    203▕         if (!stream_set_blocking($this->socket, true)) {
    204▕             throw new ClientException(sprintf('error set block mode on "%s" socket', $socketUri));

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/CustomService.php:284
      PAMI\Client\Impl\ClientImpl::open()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()

[2025-04-24 17:30:29] INFO: Starting custom call routing invoke script...
[2025-04-30 06:57:23] INFO: Starting custom call routing invoke script...

   PAMI\Client\Exception\ClientException 

  error: "Connection refused" while create socket "tcp://solutionsv3.tclcontactplus.com:5038"

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:200
    196▕             STREAM_CLIENT_CONNECT,
    197▕             $this->context
    198▕         );
    199▕         if ($this->socket === false) {
  ➜ 200▕             throw new ClientException(sprintf('error: "%s" while create socket "%s"', $errstr, $socketUri));
    201▕         }
    202▕         // set socket in block mode
    203▕         if (!stream_set_blocking($this->socket, true)) {
    204▕             throw new ClientException(sprintf('error set block mode on "%s" socket', $socketUri));

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/CustomService.php:284
      PAMI\Client\Impl\ClientImpl::open()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()

[2025-04-30 06:57:24] INFO: Starting custom call routing invoke script...

   PAMI\Client\Exception\ClientException 

  error: "Connection refused" while create socket "tcp://solutionsv3.tclcontactplus.com:5038"

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:200
    196▕             STREAM_CLIENT_CONNECT,
    197▕             $this->context
    198▕         );
    199▕         if ($this->socket === false) {
  ➜ 200▕             throw new ClientException(sprintf('error: "%s" while create socket "%s"', $errstr, $socketUri));
    201▕         }
    202▕         // set socket in block mode
    203▕         if (!stream_set_blocking($this->socket, true)) {
    204▕             throw new ClientException(sprintf('error set block mode on "%s" socket', $socketUri));

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/CustomService.php:284
      PAMI\Client\Impl\ClientImpl::open()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()

[2025-04-30 06:57:26] INFO: Starting custom call routing invoke script...

   PAMI\Client\Exception\ClientException 

  error: "Connection refused" while create socket "tcp://solutionsv3.tclcontactplus.com:5038"

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:200
    196▕             STREAM_CLIENT_CONNECT,
    197▕             $this->context
    198▕         );
    199▕         if ($this->socket === false) {
  ➜ 200▕             throw new ClientException(sprintf('error: "%s" while create socket "%s"', $errstr, $socketUri));
    201▕         }
    202▕         // set socket in block mode
    203▕         if (!stream_set_blocking($this->socket, true)) {
    204▕             throw new ClientException(sprintf('error set block mode on "%s" socket', $socketUri));

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/CustomService.php:284
      PAMI\Client\Impl\ClientImpl::open()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()

[2025-04-30 06:57:30] INFO: Starting custom call routing invoke script...

   PAMI\Client\Exception\ClientException 

  error: "Connection refused" while create socket "tcp://solutionsv3.tclcontactplus.com:5038"

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:200
    196▕             STREAM_CLIENT_CONNECT,
    197▕             $this->context
    198▕         );
    199▕         if ($this->socket === false) {
  ➜ 200▕             throw new ClientException(sprintf('error: "%s" while create socket "%s"', $errstr, $socketUri));
    201▕         }
    202▕         // set socket in block mode
    203▕         if (!stream_set_blocking($this->socket, true)) {
    204▕             throw new ClientException(sprintf('error set block mode on "%s" socket', $socketUri));

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/CustomService.php:284
      PAMI\Client\Impl\ClientImpl::open()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()

[2025-04-30 06:57:34] INFO: Starting custom call routing invoke script...

   PAMI\Client\Exception\ClientException 

  error: "Connection refused" while create socket "tcp://solutionsv3.tclcontactplus.com:5038"

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:200
    196▕             STREAM_CLIENT_CONNECT,
    197▕             $this->context
    198▕         );
    199▕         if ($this->socket === false) {
  ➜ 200▕             throw new ClientException(sprintf('error: "%s" while create socket "%s"', $errstr, $socketUri));
    201▕         }
    202▕         // set socket in block mode
    203▕         if (!stream_set_blocking($this->socket, true)) {
    204▕             throw new ClientException(sprintf('error set block mode on "%s" socket', $socketUri));

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/CustomService.php:284
      PAMI\Client\Impl\ClientImpl::open()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()

[2025-04-30 11:37:51] INFO: Starting custom call routing invoke script...
[2025-04-30 13:24:08] INFO: Incoming call from ***********
[2025-04-30 13:36:21] INFO: Starting custom call routing invoke script...
[2025-05-02 11:08:25] INFO: Starting custom call routing invoke script...
[2025-05-02 11:20:39] INFO: Starting custom call routing invoke script...
[2025-05-02 11:43:07] INFO: Starting custom call routing invoke script...
[2025-05-02 12:50:55] INFO: Starting custom call routing invoke script...
[2025-05-05 15:25:04] INFO: Starting custom call routing invoke script...

   PAMI\Client\Exception\ClientException 

  Error reading

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:292
    288▕         // Read something.
    289▕         //$read = @fread($this->socket, 65535);
    290▕         $read = @fread($this->socket, 8192);
    291▕         if ($read === false || (empty($read) && @feof($this->socket))) {
  ➜ 292▕             throw new ClientException('Error reading');
    293▕         }
    294▕         $this->currentProcessingMessage .= $read;
    295▕         // If we have a complete message, then return it. Save the rest for
    296▕         // later.

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/CustomService.php:287
      PAMI\Client\Impl\ClientImpl::process()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()

[2025-05-05 17:12:23] INFO: Starting custom call routing invoke script...

   PAMI\Client\Exception\ClientException 

  error: "Connection refused" while create socket "tcp://127.0.0.1:5038"

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:200
    196▕             STREAM_CLIENT_CONNECT,
    197▕             $this->context
    198▕         );
    199▕         if ($this->socket === false) {
  ➜ 200▕             throw new ClientException(sprintf('error: "%s" while create socket "%s"', $errstr, $socketUri));
    201▕         }
    202▕         // set socket in block mode
    203▕         if (!stream_set_blocking($this->socket, true)) {
    204▕             throw new ClientException(sprintf('error set block mode on "%s" socket', $socketUri));

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/CustomService.php:284
      PAMI\Client\Impl\ClientImpl::open()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()

[2025-05-05 17:12:25] INFO: Starting custom call routing invoke script...

   PAMI\Client\Exception\ClientException 

  error: "Connection refused" while create socket "tcp://127.0.0.1:5038"

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:200
    196▕             STREAM_CLIENT_CONNECT,
    197▕             $this->context
    198▕         );
    199▕         if ($this->socket === false) {
  ➜ 200▕             throw new ClientException(sprintf('error: "%s" while create socket "%s"', $errstr, $socketUri));
    201▕         }
    202▕         // set socket in block mode
    203▕         if (!stream_set_blocking($this->socket, true)) {
    204▕             throw new ClientException(sprintf('error set block mode on "%s" socket', $socketUri));

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/CustomService.php:284
      PAMI\Client\Impl\ClientImpl::open()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()

[2025-05-05 17:12:28] INFO: Starting custom call routing invoke script...

   PAMI\Client\Exception\ClientException 

  error: "Connection refused" while create socket "tcp://127.0.0.1:5038"

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:200
    196▕             STREAM_CLIENT_CONNECT,
    197▕             $this->context
    198▕         );
    199▕         if ($this->socket === false) {
  ➜ 200▕             throw new ClientException(sprintf('error: "%s" while create socket "%s"', $errstr, $socketUri));
    201▕         }
    202▕         // set socket in block mode
    203▕         if (!stream_set_blocking($this->socket, true)) {
    204▕             throw new ClientException(sprintf('error set block mode on "%s" socket', $socketUri));

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/CustomService.php:284
      PAMI\Client\Impl\ClientImpl::open()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()

[2025-05-05 17:12:31] INFO: Starting custom call routing invoke script...

   PAMI\Client\Exception\ClientException 

  error: "Connection refused" while create socket "tcp://127.0.0.1:5038"

  at /home/<USER>/vendor/chan-sccp/pami/src/PAMI/Client/Impl/ClientImpl.php:200
    196▕             STREAM_CLIENT_CONNECT,
    197▕             $this->context
    198▕         );
    199▕         if ($this->socket === false) {
  ➜ 200▕             throw new ClientException(sprintf('error: "%s" while create socket "%s"', $errstr, $socketUri));
    201▕         }
    202▕         // set socket in block mode
    203▕         if (!stream_set_blocking($this->socket, true)) {
    204▕             throw new ClientException(sprintf('error set block mode on "%s" socket', $socketUri));

      [2m+1 vendor frames [22m

  2   /home/<USER>/app/Console/Commands/CustomService.php:284
      PAMI\Client\Impl\ClientImpl::open()
      [2m+12 vendor frames [22m

  15  /home/<USER>/artisan:35
      Illuminate\Foundation\Console\Kernel::handle()

