<?php

namespace App\Http\Controllers;

use App\Models\PauseReason;
use App\Models\QueueLog;
use App\Models\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;



class BreakReportController extends Controller
{
    public function getBreakReportColumns(): JsonResponse
    {
        try {
            $columns = DB::table('queue_log')->select(['data1'])->distinct()->where('Event', 'PAUSE')->pluck('data1')->map(function ($a) {
                return $a === '' ? 'Others' : $a;
            })->toArray();
            $columns = array_merge(['AgentId', 'AgentName'], $columns, ['Total']);
            return response()->json($columns);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getBreakReportDEV(Request $request): JsonResponse
    {
        try {
            $date = $request->date ?? Carbon::now()->format('Y-m-d');
            $agents = DB::table('queue_log')->select(['Agent'])->distinct()->where('Event', 'PAUSE')->whereDate('time' , $date)->pluck('Agent');
            $breaks = DB::table('queue_log')->select(['data1'])->distinct()->where('Event', 'PAUSE')->whereDate('time', $date)->pluck('data1')->map(function ($a) {
                return $a == '' ? 'Not-Selected' : $a;
            })->toArray();

            if(isset($request->agents)) {
                $agents = $request->agents;
            }

            if(isset($request->breaks)) {
                $breaks = $request->breaks;
            }
            $data = [];
            foreach ($agents as $key => $agent) {
                $totalBreak = 0;
                $user = User::query()->where('name', $agent)->get()->first();
                foreach ($breaks as $break) {
                    $breakTime=0;

                        $breakData = DB::select("SELECT * FROM queue_log WHERE EVENT IN ('PAUSE', 'UNPAUSE') AND data1 = '$break' AND Agent = '$agent' AND DATE(time) = '$date' ORDER BY TIME ASC");

                        for($x=0; $x < count($breakData); $x++ ){
                            if($breakData[$x]->Event === 'PAUSE') {
                                // Search for UNPAUSE
                                if($x !== count($breakData)-1 && $breakData[$x + 1]->Event === 'UNPAUSE') {
                                    // Take difference
                                    $breakTime += Carbon::parse($breakData[$x + 1]->time)->diffInSeconds($breakData[$x]->time);
                                }else if($date === Carbon::now()->format('Y-m-d')) {
                                    $unpause = Carbon::now()->format('Y-m-d H:i:s');
                                    $breakTime += Carbon::parse($unpause)->diffInSeconds($breakData[$x]->time);
                                }else{
                                    $unpause = Carbon::parse($date)->copy()->endOfDay()->format('Y-m-d H:i:s');
                                    $breakTime += Carbon::parse($unpause)->diffInSeconds($breakData[$x]->time);
                                }
                            }
                        }
                        $totalBreak += $breakTime;

                        $data[$key][($break == Null) ? 'Not-Selected' : $break] = sprintf('%02d:%02d:%02d', ($breakTime/ 3600),($breakTime/ 60 % 60), $breakTime% 60);
                        $data[$key]["AgentName"] = $agent;
                        $data[$key]["AgentId"] = $user->auth_username ?? null;
                        $data[$key]["Total"] = sprintf('%02d:%02d:%02d', ($totalBreak/ 3600),($totalBreak/ 60 % 60), $totalBreak% 60);

                }
            }
            $columns = array_merge(['AgentId', 'AgentName'], $breaks, ['Total']);
            return response()->json([$data ,$columns]);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getBreakReport(Request $request): JsonResponse
    {
        $request->validate(['date' => 'required']);
        try {
            $agentIds = $request->input('agents', []);
            $date = $request->input('date');
            $breakTypes = $request->input('breaks', []);

            $baseQueueLogQuery = QueueLog::query()->whereDate('time', $date)->whereIn('Event',['PAUSE','UNPAUSE'])->whereNotIn('data1', ['Not Submit Workcode Yet', 'After login set not ready']);

            if (!empty($breakTypes)) {
                $baseQueueLogQuery->whereIn('data1', $breakTypes);
            } else {
                // $breakTypes = DB::table('queue_log')->select('data1')->distinct()->where('Event', 'PAUSE')->where('data1', '!=', '')->pluck('data1');
                $breakTypes = PauseReason::query()->get()->pluck('name')->toArray();
                $baseQueueLogQuery->whereIn('data1', $breakTypes);
            }

            if(!empty($agentIds)) {
                $users = User::query()->whereIn('username', $agentIds)->get();
            }
            else {
                $users = QueueLog::select('Agent as name', 'users.auth_username', 'users.username')
                                    ->leftJoin('users', 'users.name', '=', 'queue_log.Agent')
                                    ->where('queue_log.Agent', '<>', 'NONE')
                                    ->groupBy('queue_log.Agent', 'users.auth_username')
                                    ->orderByDesc('queue_log.time')
                                    ->distinct()
                                    ->get();
            }

            $data = [];
            foreach($users as $user) {
                $userQueueLog = (clone $baseQueueLogQuery)->where('Agent', $user->name)->get();

                $breakDurations = array_fill_keys($breakTypes, 0);
                $totalDuration = 0;

                $pauseStartTimes = [];
                foreach ($userQueueLog as $logEntry) {
                    $breakType = $logEntry->data1;
                    $eventTime = \Carbon\Carbon::parse($logEntry->time);

                    if ($logEntry->Event == 'PAUSE') {
                        // Store the pause start time for the break type
                        $pauseStartTimes[$breakType] = $eventTime;

                    } elseif ($logEntry->Event == 'UNPAUSE' && isset($pauseStartTimes[$breakType])) {
                        // Calculate the time difference when unpausing
                        $pauseStartTime = $pauseStartTimes[$breakType];
                        $duration = $eventTime->diffInSeconds($pauseStartTime);

                        // Add the duration to the appropriate break type
                        $breakDurations[$breakType] += $duration;
                        $totalDuration += $duration;

                        // Reset the pause start time
                        unset($pauseStartTimes[$breakType]);
                    }
                }

                // Convert seconds into H:i:s format
                foreach ($breakDurations as $key => $seconds) {
                    $breakSeconds = round($seconds);
                    $breakDurations[$key] = sprintf('%02d:%02d:%02d', ($breakSeconds / 3600), ($breakSeconds / 60 % 60), $breakSeconds % 60);
                }

                $totalSeconds = round($totalDuration);
                $totalFormatted = sprintf('%02d:%02d:%02d', ($totalSeconds / 3600), ($totalSeconds / 60 % 60), $totalSeconds % 60);

                $data[] = array_merge(
                    [
                        "AgentId" => $user->username,
                        "AgentName" => $user->name
                    ],
                    $breakDurations,  // Add break durations at the same level
                    ['Total' => $totalFormatted]  // Add total duration
                );
            }

            return response()->json($data);

        } catch (Exception $e) {
            return response()->json(['message' => $e->getMessage()], 500);
        }
    }

    public function getAllBreaks(Request $request): JsonResponse {
        try {
            $breakTypes = PauseReason::query()->get()->pluck('name')->toArray();
            $breakTypes = array_merge(['AgentId', 'AgentName', 'Total'], $breakTypes);

            return response()->json($breakTypes);
        } catch (Exception $e) {
            return response()->json(['message' => $e->getMessage()], 500);
        }
    }
}