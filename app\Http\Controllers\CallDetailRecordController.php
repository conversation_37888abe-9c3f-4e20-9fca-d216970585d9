<?php

namespace App\Http\Controllers;

use App\Models\Cdr;
use App\Exports\CdrExport;
use App\Exports\CdrFilterExport;
use App\Models\QueueLog;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Pagination\Paginator;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\AbandonCallDataReportExport;

class CallDetailRecordController extends Controller
{
    public function getCallDetailRecords(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $date = Carbon::now();
            $cdr = Cdr::query()
                ->select(['users.name', 'work_codes.name as workcode', 'uniqueid', 'recordingfile', 'accountcode', 'src', 'dst', 'channel', 'dstchannel', 'disposition', 'duration', 'start', 'end', 'transcription'])
                ->leftJoin("users", function ($join) {
                    $join->on("cdr.channel", "LIKE", DB::raw("CONCAT('%PJSIP/', users.auth_username, '%')"))
                        ->orOn("cdr.dstchannel", "LIKE", DB::raw("CONCAT('%PJSIP/', users.auth_username, '%')"));
                })
                ->leftJoin('queue_log', function ($join) {
                    $join->on('queue_log.callid', '=', 'cdr.uniqueid')
                        ->where('queue_log.Event', '=', 'Workcode');
                })
                ->leftJoin('work_codes', 'work_codes.id', '=', 'queue_log.data1')
                ->where('lastapp', '!=', 'AGI')
                ->whereDate('start', '>=', $date->startOfDay())
                ->whereDate('end', '<=', $date->endOfDay())
                ->orderBy('start', 'desc')->paginate($request->records ?? 15);
            return response()->json($cdr);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getCallDetailRecordsFilteredBKP(Request $request): \Illuminate\Http\JsonResponse
    {
        ini_set('memory_limit', '-1');
        try {
            $cdr = Cdr::query()->select(['uniqueid', 'recordingfile', 'accountcode', 'src', 'dst', 'channel', 'dstchannel', 'disposition', 'duration', 'start', 'end']);
            $q = "select u.name, c.*,w.name as workcode from cdr c
            left join users u on(c.channel LIKE  CONCAT('%PJSIP/', u.auth_username, '%') OR c.dstchannel LIKE CONCAT('%PJSIP/', u.auth_username, '%'))
	        left join queue_log q on (q.callid=c.uniqueid AND q.Event = 'Workcode' )
            left join work_codes w on w.id = q.data1
	        where lastapp <> 'AGI'";

            if ($request->has('range') && is_array($request->range)) {
                $start = Carbon::parse($request->range[0])->timezone('Asia/Karachi');
                $end = Carbon::parse($request->range[1])->timezone("Asia/Karachi");
                $cdr->whereBetween("start", [$start->toDateTimeString(), $end->toDateTimeString()]);
                $q .= "and start BETWEEN '{$start->toDateTimeString()}' and '{$end->toDateTimeString()}' ";
            }

            if ($request->has('source') && $request->source != "" && isset($request->source)) {
                $cdr->where('src', $request->source);
                $q .= " and src = '$request->source' ";
            }

            if ($request->has('destination') && $request->destination != "" && isset($request->destination)) {
                $cdr->where('dst', $request->destination);
                $q .= " and dst = '$request->destination' ";
            }

            if ($request->has('agents') && is_array($request->agents)) {
                foreach ($request->agents as $index => $agent) {
                    if (count($request->agents) > 1 && $index == 0) {
                        $q .= ' and (';
                    } else if (count($request->agents) == 1 && $index == 0) {
                        $q .= " and ";
                    }

                    if ($index == 0)
                        $q .= " (channel like  '%PJSIP/$agent%' OR dstchannel LIKE '%PJSIP/$agent%')";

                    if ($index > 0)
                        $q .= " OR (channel like  '%PJSIP/$agent%' OR dstchannel LIKE '%PJSIP/$agent%')";

                    if (count($request->agents) - 1 == $index && $index > 0) {
                        $q .= ')';
                    }

                    $cdr->where(function ($query) use ($agent) {
                        $query->where('channel', 'like', "%PJSIP/$agent%");
                        $query->orWhere('dstchannel', 'like', "%PJSIP/$agent%");
                    });
                }
            }
            if ($request->has('call_status')) {
                $cdr->where('disposition', 'like', "%$request->call_status%");
                $q .= " and disposition like '%$request->call_status%'";
            }

            if ($request->has('accountcode')) {
                $cdr->where('accountcode', $request->accountcode);
                $q .= " and accountcode = '$request->accountcode'";
            }

            $cdr->orderBy('start', 'desc');
            $q .= " order by start desc";


            $data = DB::select($q);
            $perPage = $request->records;
            $result = $this->paginate($data, $perPage);
            $result->withPath($request->url());

            //            return response()->json($cdr->get());
            return response()->json($result);

        } catch (Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getCallDetailRecordsFiltered(Request $request): \Illuminate\Http\JsonResponse
    {
        ini_set('memory_limit', '-1');
        try {
            $cdr = Cdr::query()->select([
                'uniqueid',
                'recordingfile',
                'accountcode',
                'src',
                'dst',
                'channel',
                'dstchannel',
                'disposition',
                'duration',
                'start',
                'end'
            ]);

            $q = "SELECT u.name, c.*, w.name AS workcode, q.queuename 
                FROM cdr c
                LEFT JOIN users u ON (c.channel LIKE CONCAT('%PJSIP/', u.auth_username, '%') 
                                        OR c.dstchannel LIKE CONCAT('%PJSIP/', u.auth_username, '%'))
                LEFT JOIN queue_log q ON (q.callid = c.uniqueid AND (q.Event = 'Workcode' OR q.Event = 'ENTERQUEUE'))
                LEFT JOIN work_codes w ON w.id = q.data1
                WHERE lastapp <> 'AGI'";

            if ($request->has('range') && is_array($request->range)) {
                $start = Carbon::parse($request->range[0])->timezone('Asia/Karachi');
                $end = Carbon::parse($request->range[1])->timezone("Asia/Karachi");
                $cdr->whereBetween("start", [$start->toDateTimeString(), $end->toDateTimeString()]);
                $q .= " AND start BETWEEN '{$start->toDateTimeString()}' AND '{$end->toDateTimeString()}' ";
            }

            if ($request->has('source') && !empty($request->source)) {
                $cdr->where('src', $request->source);
                $q .= " AND src = '{$request->source}'";
            }

            if ($request->has('destination') && !empty($request->destination)) {
                $cdr->where('dst', $request->destination);
                $q .= " AND dst = '{$request->destination}'";
            }

            if ($request->has('agents') && is_array($request->agents)) {
                $q .= " AND (";
                foreach ($request->agents as $index => $agent) {
                    if ($index > 0) {
                        $q .= " OR ";
                    }
                    $q .= "(c.channel LIKE '%PJSIP/{$agent}%' OR c.dstchannel LIKE '%PJSIP/{$agent}%')";
                }
                $q .= ")";
            }

            if ($request->has('call_status')) {
                $cdr->where('disposition', 'like', "%{$request->call_status}%");
                $q .= " AND disposition LIKE '%{$request->call_status}%'";
            }

            if ($request->has('accountcode')) {
                $cdr->where('accountcode', $request->accountcode);
                $q .= " AND accountcode = '{$request->accountcode}'";
            }

            if ($request->has('queue') && !empty($request->queue)) {
                $q .= " AND q.queuename = '{$request->queue}'";
            }

            $cdr->orderBy('start', 'desc');
            $q .= " ORDER BY start DESC";

            $data = DB::select($q);
            $perPage = $request->records;
            $result = $this->paginate($data, $perPage);
            $result->withPath($request->url());

            return response()->json($result);
        } catch (Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }


    public function paginate($items, $perPage, $page = null)
    {
        $page = $page ?: (Paginator::resolveCurrentPage() ?: 1);
        $total = count($items);
        $currentpage = $page;
        $offset = ($currentpage * $perPage) - $perPage;
        $itemstoshow = array_slice($items, $offset, $perPage);
        return new LengthAwarePaginator($itemstoshow, $total, $perPage);
    }

    public function holdTimeReport(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $date = Carbon::now()->format('Y-m-d');
            if (isset($request->date)) {
                $date1 = Carbon::parse($request->date[0])->format('Y-m-d');
                $date2 = Carbon::parse($request->date[1])->format('Y-m-d');
                $q = " AND DATE(TIME) >= '{$date1}' AND DATE(TIME) <= '{$date2}'";
            } else
                $q = " and date(time) = '{$date}'";
            if (isset($request->status))
                echo "status";
            return response()->json(DB::select("select queuename as queue, COUNT(CASE WHEN data1 > 0 and data1 <= 10 then 1 ELSE NULL END) as '0-10', COUNT(CASE when data1 > 10 and data1 <= 20 then 1 else NULL end) as '11-20', COUNT(CASE when data1 > 20 and data1 <= 30 then 1 else NULL end) as '21-30', COUNT(case when data1 > 30 and data1 <= 40 then 1 else null end) as '31-40', COUNT(CASE when data1 > 40 and data1 <= 50 then 1 else NULL end) as '41-50', COUNT(CASE when data1 > 50 and data1 <= 60 then 1 else NULL end) as '51-60', COUNT(CASE when data1 > 60 then 1 else NULL end) as '61 >', ROUND(AVG(data1), 2) as 'avg_wait_time', MAX(CONVERT(data1, UNSIGNED)) as 'longest_wait_time', COUNT(*) as 'total' from queue_log where Event = 'CONNECT' {$q} group by queuename"));
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getCallHistory(Request $request): JsonResponse
    {
        try {
            if (isset($request->src)) {
                if ($request->src[0] === "0") {
                    $request->src = ltrim($request->src, $request->src[0]);
                }
                $cdr = Cdr::query()->where('src', 'LIKE', "%$request->src%")->orWhere('dst', 'LIKE', "%$request->src%")->limit(5)->orderBy('start', 'desc')->get()->map(function ($record) {
                    if ($record->accountcode === 'Queue') {
                        // It will be dstchannel
                        $user = User::query()->where('auth_username', $record->dstchannel)->first();
                        if ($user)
                            $record->agent = $user->name;
                        else
                            $record->agent = $record->dstchannel;
                    } else if ($record->accountcode === 'Outbound') {
                        // It will be channel
                        $user = User::query()->where('auth_username', $record->channel)->first();
                        if ($user)
                            $record->agent = $user->name;
                        else
                            $record->agent = $record->channel;
                    }
                    return $record;
                });
                return response()->json($cdr);
            } else
                return response()->json([]);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function abandonCallDataBKP(Request $request): JsonResponse
    {
        try {
            $data = QueueLog::query()->from('queue_log as q')->join('cdr as c', function ($join) {
                $join->on('c.uniqueid', '=', 'q.callid')->where('q.EVENT', '=', 'ABANDON');
            })
                ->select(DB::raw('DATE(q.time) date, TIME(q.time) time, c.src, q.data1 as position, q.data2 as origposition, q.data3 as waittime'))
                ->distinct();

            if (isset($request->range) && is_array($request->range)) {
                $date1 = Carbon::parse($request->range[0])->format("Y-m-d");
                $date2 = Carbon::parse($request->range[1])->format("Y-m-d");
                $data->whereDate('q.time', '>=', $date1)->whereDate('q.time', '<=', $date2);
            }
            if (isset($request->dst) && $request->dst != null)
                $data->where('c.src', 'LIKE', "%{$request->dst}%");
            return response()->json($data->paginate($request->pageSize));
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function abandonCallData(Request $request): JsonResponse
    {
        try {
            $data = QueueLog::query()
                ->from('queue_log as q')
                ->join('cdr as c', function ($join) {
                    $join->on('c.uniqueid', '=', 'q.callid')->where('q.EVENT', '=', 'ABANDON');
                })
                ->select(DB::raw('DATE(q.time) as date, TIME(q.time) as time, c.src, q.queuename, q.data1 as position, q.data2 as origposition, q.data3 as waittime'))->distinct();

            if (isset($request->range) && is_array($request->range)) {
                $date1 = Carbon::parse($request->range[0])->format("Y-m-d");
                $date2 = Carbon::parse($request->range[1])->format("Y-m-d");
                $data->whereBetween('q.time', [$date1 . ' 00:00:00', $date2 . ' 23:59:59']);
            }

            if (!empty($request->dst)) {
                $data->where('c.src', 'LIKE', "%{$request->dst}%");
            }

            if (!empty($request->queue)) {
                $data->where('q.queuename', $request->queue);
            }

            return response()->json($data->paginate($request->pageSize));
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }


    public function exportAbandonCallData(Request $request)
    {
        try {
            // Define the date range
            $date1 = Carbon::now()->startOfDay();
            $date2 = Carbon::now()->endOfDay();
            if ($request->has('range') && is_array($request->range)) {
                $date1 = Carbon::parse($request->range[0])->timezone('Asia/Karachi')->startOfDay();
                $date2 = Carbon::parse($request->range[1])->timezone('Asia/Karachi')->endOfDay();
            }

            $data = QueueLog::query()
                ->from('queue_log as q')
                ->select([
                    DB::raw('DATE(q.time) as date'),
                    DB::raw('TIME(q.time) as time'),
                    DB::raw('(SELECT c.src FROM cdr as c WHERE c.uniqueid = q.callid LIMIT 1) as src'),
                    'q.data1 as position',
                    'q.data2 as origposition',
                    'q.data3 as waittime'
                ])
                ->where('q.EVENT', '=', 'ABANDON')
                ->whereBetween(DB::raw('DATE(q.time)'), [$date1, $date2]);

            if ($request->filled('queue')) {
                $data->where('q.queuename', $request->queue);
            }

            // Process data in chunks to handle large datasets
            $chunkSize = 1000; // Adjust based on your memory and performance requirements
            $dataArray = [];
            $data->orderBy('q.time', 'asc')->chunk($chunkSize, function ($rows) use (&$dataArray) {
                foreach ($rows as $row) {
                    $dataArray[] = $row;
                }
            });

            return Excel::download(new AbandonCallDataReportExport($dataArray), 'abandoned.xlsx');

        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function Last10AbandonCallData(Request $request): JsonResponse
    {
        try {
            $data = QueueLog::query()->from('queue_log as q')->join('cdr as c', function ($join) {
                $join->on('c.uniqueid', '=', 'q.callid')->where('q.EVENT', '=', 'ABANDON');
            })->select(DB::raw('DATE(q.time) date, TIME(q.time) time, c.src, q.data1 as position, q.data2 as origposition, q.data3 as waittime'));
            return response()->json($data->orderByDesc('time')->limit(10)->get());
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function abandonCallDataHours(): JsonResponse
    {
        try {
            $data = QueueLog::query()->from('queue_log as q')->join('cdr as c', function ($join) {
                $join->on('c.uniqueid', '=', 'q.callid')->where('q.EVENT', '=', 'ABANDON');
            })->whereRaw('TIME BETWEEN DATE_SUB(NOW(), INTERVAL 3 HOUR) AND NOW()')->
                select(DB::raw('DATE(q.time) date, TIME(q.time) time, c.src, q.data1 as duration'))->get();
            return response()->json($data);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function abandonCallDifferenceReportBKP(Request $request): JsonResponse
    {
        $request->validate([
            "date" => ["required", "date"]
        ]);

        try {
            //            $query1 = "select DISTINCT c.src from queue_log q join cdr c on q.callid = c.uniqueid where q.Event = 'ABANDON' and date(q.time) = '{$request->date}'";
//            $query2 = "select DISTINCT q.callid from queue_log q where q.Event = 'ABANDON' and date(q.time) = '{$request->date}'";
//            $qMain1 = "select c.src as number, c.start as c_time, c.accountcode from cdr c where c.src in ($query1) and c.uniqueid not in ($query2) and date(c.start) = '{$request->date}'";
//            $qMain2 = "select c.dst as number, c.start as c_time, c.accountcode from cdr c where c.dst in ($query1) and c.uniqueid not in ($query2) and date(c.start) = '{$request->date}'";
//            $query = "$qMain1 UNION $qMain2";
//            $data = collect(DB::select($query));
//            $abandoned = "select c.src as number, c.start from queue_log q join cdr c on q.callid = c.uniqueid where q.Event = 'ABANDON' and date(q.time) = '{$request->date}' order by c.start desc";
//            $abandoned = DB::select($abandoned);
//
//            foreach ($abandoned as $item) {
//                $item->called_back = 'N/A';
//                $item->difference = 'N/A';
//                $item->cb_type = 'N/A';
//                $number = $data->where('number', $item->number)->last();
//                if($number) {
//                    $item->called_back = $number->c_time;
//                    $item->difference = gmdate("H:i:s", Carbon::parse($item->start)->diffInSeconds($number->c_time));
//                    $item->cb_type = $number->accountcode;
//                }
//            }
//            $data = DB::select("SELECT DATE_FORMAT(a.time, '%d %M %Y %r') as start, DATE_FORMAT(b.time, '%d %M %Y %r') as called_back, a.src as number,a.accountcode as cb_type, TIME_FORMAT(TIMEDIFF(b.time,a.time),'%T') as difference FROM (SELECT * FROM cdr c INNER JOIN queue_log q ON (q.callid = c.uniqueid AND q.event = 'ABANDON' AND DATE(c.start) = '$request->date') ORDER BY c.start DESC) AS a LEFT JOIN (SELECT * FROM cdr c INNER JOIN queue_log q ON (q.callid = c.uniqueid AND DATE(c.start) = '$request->date') WHERE accountcode = 'Outbound' OR (accountcode = 'Queue' AND disposition = 'ANSWERED') ORDER BY c.start DESC) AS b ON (a.src = b.src and a.time < b.time) group by a.uniqueid;");
//            $data = DB::select("SELECT  b.startTime,  DATE_FORMAT(a.start, '%d %M %Y %r') AS start,   DATE_FORMAT(b.startTime, '%d %M %Y %r') AS called_back,   a.src AS number,   a.accountcode AS cb_type, b.accountcode,   TIME_FORMAT(TIMEDIFF(b.startTime, a.start), '%T') AS difference  FROM   (  SELECT     *   FROM     cdr c      INNER JOIN queue_log q        ON (         q.callid = c.uniqueid          AND q.event = 'ABANDON'          AND DATE(c.start) = '$request->date'       )  group by src  ORDER BY c.start DESC    )   AS a    LEFT JOIN      (   select *, max(start) as startTime from cdr where date(start) = '$request->date'  and (accountcode = 'Outbound' OR accountcode= 'Queue')  and disposition = 'ANSWERED' group by src   ) AS b      ON (a.src = b.src   AND a.start < b.startTime)");

            //            $data = DB::select("SELECT    DATE_FORMAT(a.time, '%d %M %Y %r') AS start,  DATE_FORMAT(b.start, '%d %M %Y %r') AS called_back,   a.src AS number,  TIME_FORMAT(TIMEDIFF(b.start, a.time), '%T') AS difference  FROM   (SELECT      *    FROM     cdr c      INNER JOIN queue_log q        ON (         q.callid = c.uniqueid          AND q.event = 'ABANDON'          AND DATE(c.start) = '$request->date'       )    ORDER BY c.start DESC) AS a    LEFT JOIN      (SELECT        *      FROM       cdr c    WHERE (accountcode = 'Outbound'      OR         accountcode = 'Queue') and  disposition = 'ANSWERED'  and  DATE(c.start) = '$request->date' order by start desc) AS b      ON ((a.src = b.src   OR a.dst = b.dst  ) AND a.time < b.start)  GROUP BY a.uniqueid");

            $data = DB::select("SELECT    a.src AS number,   DATE_FORMAT(a.time, '%d %M %Y %r') AS abandon_time,   b.accountcode AS CallDirection,   DATE_FORMAT(b.start, '%d %M %Y %r') AS followupTime,   TIME_FORMAT(TIMEDIFF(b.start, a.time), '%T') AS difference  FROM   (SELECT      *    FROM     cdr      INNER JOIN queue_log        ON (         queue_log.callid = cdr.uniqueid          AND queue_log.event = 'ABANDON'          AND DATE(cdr.start) = '$request->date'       )    ORDER BY cdr.start DESC) AS a    LEFT JOIN      (SELECT        *      FROM       cdr      WHERE cdr.accountcode = 'Queue'        AND cdr.disposition = 'ANSWERED'        AND cdr.dstchannel <> 'null'        AND DATE(cdr.start) = '$request->date'       OR cdr.accountcode = 'Outbound'        AND DATE(cdr.start) = '$request->date'      ORDER BY cdr.start DESC) AS b      ON (       b.src LIKE CONCAT('%', a.src, '%')        AND a.time < b.start        OR b.dst LIKE CONCAT('%', a.src, '%')        AND a.time < b.start     )  GROUP BY a.uniqueid;");

            return response()->json($data);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function abandonCallDifferenceReport(Request $request): JsonResponse
    {
        $request->validate([
            "date" => ["required", "date"],
        ]);

        try {
            $queueFilter = "";
            if (!empty($request->queue)) {
                $queueFilter = " AND queue_log.queuename = '{$request->queue}'";
            }

            $query = "SELECT    
                a.src AS number,   
                DATE_FORMAT(a.time, '%d %M %Y %r') AS abandon_time,   
                b.accountcode AS CallDirection,   
                DATE_FORMAT(b.start, '%d %M %Y %r') AS followupTime,   
                TIME_FORMAT(TIMEDIFF(b.start, a.time), '%T') AS difference  
            FROM   
                (SELECT * FROM cdr  
                    INNER JOIN queue_log  
                    ON (queue_log.callid = cdr.uniqueid  
                        AND queue_log.event = 'ABANDON'  
                        AND DATE(cdr.start) = '{$request->date}'
                        $queueFilter)  
                    ORDER BY cdr.start DESC) AS a  
            LEFT JOIN  
                (SELECT * FROM cdr  
                    WHERE (cdr.accountcode = 'Queue'  
                        AND cdr.disposition = 'ANSWERED'  
                        AND cdr.dstchannel IS NOT NULL  
                        AND DATE(cdr.start) = '{$request->date}')  
                    OR (cdr.accountcode = 'Outbound'  
                        AND DATE(cdr.start) = '{$request->date}')  
                    ORDER BY cdr.start DESC) AS b  
            ON (b.src LIKE CONCAT('%', a.src, '%')  
                AND a.time < b.start  
                OR b.dst LIKE CONCAT('%', a.src, '%')  
                AND a.time < b.start)  
            GROUP BY a.uniqueid;";

            $data = DB::select($query);
            return response()->json($data);

        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }


    public function cdrExport()
    {

        return Excel::download(new cdrExport(), 'CallDetailReport.csv');
    }

    public function cdrExportFilter(Request $request)
    {

        $cdr = Cdr::query()->select(['uniqueid', 'recordingfile', 'accountcode', 'src', 'dst', 'channel', 'dstchannel', 'disposition', 'duration', 'start', 'end']);
        $q = "select u.name, c.*,w.name as workcode from cdr c
        left join users u on(c.channel LIKE  CONCAT('%PJSIP/', u.auth_username, '%') OR c.dstchannel LIKE CONCAT('%PJSIP/', u.auth_username, '%'))
        left join queue_log q on (q.callid=c.uniqueid AND q.Event = 'Workcode' )
        left join work_codes w on w.id = q.data1
        where lastapp <> 'AGI'";
        //            $q = "select * from cdr where 1 ";

        if ($request->has('range') && is_array($request->range)) {
            $start = Carbon::parse($request->range[0])->timezone('Asia/Karachi');
            $end = Carbon::parse($request->range[1])->timezone("Asia/Karachi");
            $cdr->whereBetween("start", [$start->toDateTimeString(), $end->toDateTimeString()]);
            $q .= "and start BETWEEN '{$start->toDateTimeString()}' and '{$end->toDateTimeString()}' ";
        }

        if ($request->has('source') && $request->source != "" && isset($request->source)) {
            $cdr->where('src', $request->source);
            $q .= " and src = '$request->source' ";
        }

        if ($request->has('destination') && $request->destination != "" && isset($request->destination)) {
            $cdr->where('dst', $request->destination);
            $q .= " and dst = '$request->destination' ";
        }

        if ($request->has('agents') && is_array($request->agents)) {
            foreach ($request->agents as $index => $agent) {
                if (count($request->agents) > 1 && $index == 0) {
                    $q .= ' and (';
                } else if (count($request->agents) == 1 && $index == 0) {
                    $q .= " and ";
                }

                if ($index == 0)
                    $q .= " (channel like  '%PJSIP/$agent%' OR dstchannel LIKE '%PJSIP/$agent%')";

                if ($index > 0)
                    $q .= " OR (channel like  '%PJSIP/$agent%' OR dstchannel LIKE '%PJSIP/$agent%')";

                if (count($request->agents) - 1 == $index && $index > 0) {
                    $q .= ')';
                }

                $cdr->where(function ($query) use ($agent) {
                    $query->where('channel', 'like', "%PJSIP/$agent%");
                    $query->orWhere('dstchannel', 'like', "%PJSIP/$agent%");
                });
            }
        }
        if ($request->has('call_status')) {
            $cdr->where('disposition', 'like', "%$request->call_status%");
            $q .= " and disposition like '%$request->call_status%'";
        }

        if ($request->has('accountcode')) {
            $cdr->where('accountcode', $request->accountcode);
            $q .= " and accountcode = '$request->accountcode'";
        }

        $cdr->orderBy('start', 'desc');
        $q .= " order by start desc";

        $data = DB::select($q);
        return Excel::download(new cdrFilterExport($data), 'FilteredCallDetailReport.csv');

    }


    public function abandonCallReport(Request $request): JsonResponse
    {
        try {

            $date1 = Carbon::now()->startOfDay()->format("Y-m-d");
            $date2 = Carbon::now()->endOfDay()->format("Y-m-d");

            if (isset($request->range) && is_array($request->range)) {
                $date1 = Carbon::parse($request->range[0])->format("Y-m-d");
                $date2 = Carbon::parse($request->range[1])->format("Y-m-d");
            }

            $data = QueueLog::query()
                ->from('queue_log as q')
                // ->join('cdr as c', function ($join) {
                //     $join->on('c.uniqueid', '=', 'q.callid')->where('q.EVENT', '=', 'ABANDON');
                // })
                ->join('cdr as c', 'c.uniqueid', '=', 'q.callid')
                ->select(
                    DB::raw('DATE(q.time) as date'),
                    DB::raw('TIME(q.time) as time'),
                    'c.uniqueid',
                    'q.callid',
                    'c.src',
                    'c.dst',
                    'q.data3 as waittime',
                    'q.EVENT',
                    'q.status'
                )
                // ->whereDate('q.time', '>=', $date1)
                // ->whereDate('q.time', '<=', $date2)
                // ->where('q.status', '=', '0')
                // ->groupBy('c.uniqueid');
                ->where('q.EVENT', 'ABANDON')
                ->whereBetween(DB::raw('DATE(q.time)'), [$date1, $date2])
                ->where('q.status', '0');

            return response()->json($data->paginate($request->pageSize));
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function updateStatus(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'callid' => 'required|string',
                'status' => 'required|boolean'
            ]);

            $callid = $validated['callid'];
            $status = $validated['status'];

            $affectedRows = DB::update("UPDATE queue_log SET status = ? WHERE callid = ? AND EVENT = 'ABANDON'", [$status, $callid]);

            if ($affectedRows === 0) {
                return response()->json(['error' => 'Record not found or not an ABANDON event'], 404);
            }

            if ($request->userid) {
                $date1 = Carbon::now()->startOfDay()->format("Y-m-d");
                $date2 = Carbon::now()->endOfDay()->format("Y-m-d");
                $page = 1;
                $size = 10;

                $data = QueueLog::query()->from('queue_log as q')
                    // ->join('cdr as c', function ($join) {
                    //     $join->on('c.uniqueid', '=', 'q.callid')->where('q.EVENT', '=', 'ABANDON');
                    // })
                    ->join('cdr as c', 'c.uniqueid', '=', 'q.callid')
                    ->select(
                        DB::raw('DATE(q.time) as date'),
                        DB::raw('TIME(q.time) as time'),
                        'c.uniqueid',
                        'q.callid',
                        'c.src',
                        'c.dst',
                        'q.data3 as waittime',
                        'q.EVENT',
                        'q.status'
                    )
                    // ->whereDate('q.time', '>=', $date1)
                    // ->whereDate('q.time', '<=', $date2)
                    // ->where('q.status', '=', '0')
                    // ->groupBy('c.uniqueid');
                    ->where('q.EVENT', 'ABANDON')
                    ->whereBetween(DB::raw('DATE(q.time)'), [$date1, $date2])
                    ->where('q.status', '0');


                $page = $request->current;
                $size = $request->pageSize;

                // Paginate the grouped records
                $paginatedData = $data->paginate($size, ['*'], 'page', $page);

                $response = [
                    'user_id' => $request->userid,
                    'currentPage' => $paginatedData->currentPage(),
                    'pageSize' => $paginatedData->perPage(),
                    'total' => $paginatedData->total(),
                    'data' => ['data' => $paginatedData->items()],
                ];

                // Log::info("success dispatch from controller: " . json_encode($response, JSON_PRETTY_PRINT));
                broadcast(new \App\Events\GetAbandonCallReport($response));
            }

            return response()->json(['message' => 'Status updated successfully']);

        } catch (\Exception $exception) {
            return response()->json(['error' => $exception->getMessage()], 500);
        }
    }


}
